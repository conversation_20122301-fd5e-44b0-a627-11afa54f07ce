<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG AI 流式对话</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 85vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .mode-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .mode-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .message.user .message-avatar {
            background: #667eea;
            color: white;
            order: 1;
        }

        .message.assistant .message-avatar {
            background: #4CAF50;
            color: white;
        }

        .streaming-message {
            border-right: 2px solid #667eea;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { border-right-color: #667eea; }
            51%, 100% { border-right-color: transparent; }
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 22px;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-style: italic;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #c62828;
        }

        .status-bar {
            background: #f0f0f0;
            padding: 8px 20px;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-links {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            text-align: center;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .status-dot.offline {
            background: #f44336;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
                border-radius: 10px;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <button class="mode-toggle" onclick="toggleMode()">🚀 流式模式</button>
            <h1>🤖 RAG AI 流式对话</h1>
            <p>实时流式响应 - 更自然的对话体验</p>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                ⚡ 欢迎使用RAG AI流式对话！<br>
                体验实时生成的AI回复，就像真人对话一样自然。
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <textarea
                    id="messageInput"
                    class="input-field"
                    placeholder="输入您的问题..."
                    rows="1"
                ></textarea>
                <button id="sendButton" class="send-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">连接中...</span>
            </div>
            <div id="responseTime"></div>
        </div>

        <div class="nav-links">
            <a href="/web/index-builder.html">🔍 索引构建器</a>
            <a href="/web/index.html">💬 标准对话</a>
            <a href="/docs">📚 API文档</a>
            <a href="/health">🏥 服务状态</a>
        </div>
    </div>

    <script>
        class StreamChatApp {
            constructor() {
                this.apiUrl = 'http://localhost:8000';
                this.messagesContainer = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.responseTime = document.getElementById('responseTime');
                this.isStreaming = false;
                this.streamMode = true;

                this.init();
            }

            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                });

                this.checkServiceStatus();
                setInterval(() => this.checkServiceStatus(), 30000); // 每30秒检查一次
            }

            async checkServiceStatus() {
                try {
                    const response = await fetch(`${this.apiUrl}/health`);
                    if (response.ok) {
                        this.statusDot.className = 'status-dot';
                        this.statusText.textContent = '服务正常';
                    } else {
                        throw new Error('Service unavailable');
                    }
                } catch (error) {
                    this.statusDot.className = 'status-dot offline';
                    this.statusText.textContent = '服务离线';
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.sendButton.disabled) return;

                this.addMessage(message, 'user');
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                this.sendButton.disabled = true;

                const startTime = Date.now();

                try {
                    if (this.streamMode) {
                        await this.sendStreamMessage(message);
                    } else {
                        await this.sendNormalMessage(message);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    this.showError(`发送消息失败: ${error.message}`);
                } finally {
                    this.sendButton.disabled = false;
                    this.messageInput.focus();

                    const responseTimeMs = Date.now() - startTime;
                    this.responseTime.textContent = `响应时间: ${responseTimeMs}ms`;
                }
            }

            async sendStreamMessage(message) {
                const response = await fetch(`${this.apiUrl}/chat/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                // 创建流式消息容器
                const messageDiv = this.createMessageElement('', 'assistant');
                const messageContent = messageDiv.querySelector('.message-content');
                messageContent.classList.add('streaming-message');

                this.removeWelcomeMessage();
                this.messagesContainer.appendChild(messageDiv);

                let fullResponse = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data === '[DONE]') {
                                    messageContent.classList.remove('streaming-message');
                                    return;
                                } else if (data === '[ERROR]') {
                                    throw new Error('Stream error');
                                } else if (data.trim()) {
                                    fullResponse += data;
                                    messageContent.textContent = fullResponse;
                                    this.scrollToBottom();
                                }
                            }
                        }
                    }
                } finally {
                    messageContent.classList.remove('streaming-message');
                }
            }

            async sendNormalMessage(message) {
                const response = await fetch(`${this.apiUrl}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        stream: false
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }

                const data = await response.json();
                this.addMessage(data.response, 'assistant');
            }

            createMessageElement(content, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = sender === 'user' ? '👤' : '🤖';

                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.textContent = content;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);

                return messageDiv;
            }

            addMessage(content, sender) {
                const messageDiv = this.createMessageElement(content, sender);
                this.removeWelcomeMessage();
                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }

            removeWelcomeMessage() {
                const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.remove();
                }
            }

            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                this.messagesContainer.appendChild(errorDiv);
                this.scrollToBottom();

                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.remove();
                    }
                }, 5000);
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }

        function toggleMode() {
            window.location.href = 'index.html';
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new StreamChatApp();
        });
    </script>
</body>
</html>
