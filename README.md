# RAG对话API系统

基于LangGraph的智能RAG（检索增强生成）对话系统，提供完整的后端API服务。

## 功能特性

- 🤖 **智能路由**: 自动判断使用向量数据库检索还是网络搜索
- 📚 **文档评分**: 评估检索文档的相关性，过滤无关内容
- 🔍 **幻觉检测**: 检查生成答案是否基于事实，避免幻觉
- ✅ **答案质量评估**: 确保答案回答了用户问题
- 🔄 **查询重写**: 自动优化查询以提高检索效果
- 🌊 **流式响应**: 支持实时流式输出，提供更好的用户体验
- ⚙️ **配置管理**: 完善的配置系统，支持环境变量和配置文件
- 🐳 **容器化部署**: 提供Docker和Docker Compose配置

## 系统架构

```
用户问题 → 路由判断 → 检索/搜索 → 文档评分 → 答案生成 → 质量检查 → 返回结果
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd forjob_py

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置必要的API密钥：

```env
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
```

### 3. 启动服务

```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 测试API

访问 http://localhost:8000/docs 查看API文档

或使用curl测试：

```bash
# 健康检查
curl http://localhost:8000/health

# 对话测试
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "什么是agent memory?"}'

# 流式对话
curl -X POST "http://localhost:8000/chat/stream" \
     -H "Content-Type: application/json" \
     -d '{"message": "解释一下prompt engineering"}'
```

## API接口

### 主要端点

- `GET /` - 健康检查
- `GET /health` - 服务状态
- `POST /chat` - 标准对话接口
- `POST /chat/stream` - 流式对话接口
- `POST /index/build` - 构建/重建索引
- `GET /index/status` - 索引状态

### 请求示例

#### 标准对话

```json
{
  "message": "什么是RAG系统？",
  "stream": false
}
```

#### 流式对话

```json
{
  "message": "解释一下LangGraph的工作原理"
}
```

#### 构建索引

```json
{
  "urls": [
    "https://example.com/doc1",
    "https://example.com/doc2"
  ],
  "documents": [
    "文档内容1",
    "文档内容2"
  ]
}
```

## Docker部署

### 使用Docker

```bash
# 构建镜像
docker build -t rag-api .

# 运行容器
docker run -p 8000:8000 --env-file .env rag-api
```

### 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `OPENAI_API_KEY` | - | OpenAI API密钥（必需） |
| `TAVILY_API_KEY` | - | Tavily API密钥（必需） |
| `MODEL_NAME` | `gpt-4o-mini` | OpenAI模型名称 |
| `TEMPERATURE` | `0.0` | 模型温度参数 |
| `CHUNK_SIZE` | `500` | 文档分块大小 |
| `CHUNK_OVERLAP` | `0` | 文档分块重叠 |
| `HOST` | `0.0.0.0` | 服务器主机 |
| `PORT` | `8000` | 服务器端口 |
| `LOG_LEVEL` | `info` | 日志级别 |

### 配置文件

系统支持通过 `.env` 文件或环境变量进行配置。详细配置选项请参考 `config.py` 文件。

## 开发指南

### 安装开发依赖

```bash
pip install -r requirements-dev.txt
```

### 代码格式化

```bash
black .
isort .
```

### 代码检查

```bash
flake8 .
mypy .
```

### 运行测试

```bash
pytest
```

## 项目结构

```
forjob_py/
├── main.py              # FastAPI应用入口
├── rag_service.py       # RAG服务核心逻辑
├── config.py            # 配置管理
├── requirements.txt     # 生产依赖
├── requirements-dev.txt # 开发依赖
├── Dockerfile          # Docker配置
├── docker-compose.yml  # Docker Compose配置
├── .env.example        # 环境变量模板
└── README.md           # 项目说明
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 确保在 `.env` 文件中正确设置了 `OPENAI_API_KEY` 和 `TAVILY_API_KEY`

2. **依赖安装失败**
   - 确保Python版本 >= 3.8
   - 尝试升级pip: `pip install --upgrade pip`

3. **向量数据库初始化失败**
   - 检查网络连接，确保能访问默认的URL
   - 或使用自定义文档构建索引

4. **端口占用**
   - 修改 `.env` 文件中的 `PORT` 配置
   - 或使用 `lsof -i :8000` 查看端口占用情况

### 日志查看

```bash
# 查看应用日志
tail -f app.log

# Docker环境查看日志
docker-compose logs -f rag-api
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件到 [<EMAIL>]

## 更新日志

### v1.0.0
- 初始版本发布
- 基础RAG功能实现
- FastAPI接口
- 流式响应支持
- Docker部署支持
