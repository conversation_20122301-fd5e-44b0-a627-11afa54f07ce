#!/usr/bin/env python3
"""
诊断路由问题的脚本
"""

import os
import sys
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, TimeoutError

def check_environment():
    """检查环境变量"""
    print("🔍 检查环境变量...")
    
    required_vars = ["OPENAI_API_KEY", "TAVILY_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}{value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"❌ {var}: 未设置")
            missing_vars.append(var)
    
    return len(missing_vars) == 0

def test_openai_connection():
    """测试OpenAI连接"""
    print("\n🤖 测试OpenAI连接...")
    
    try:
        from langchain_openai import ChatOpenAI
        
        # 创建带超时的LLM实例
        llm = ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.1,
            request_timeout=10,  # 10秒超时
            max_retries=1
        )
        
        print("   正在测试简单调用...")
        start_time = time.time()
        
        # 测试简单调用
        response = llm.invoke("Hello, respond with just 'OK'")
        
        end_time = time.time()
        print(f"✅ OpenAI连接成功 (耗时: {end_time - start_time:.2f}秒)")
        print(f"   响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI连接失败: {str(e)}")
        return False

def test_question_router():
    """测试问题路由器"""
    print("\n🧭 测试问题路由器...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        
        print("   正在初始化RAG服务...")
        rag_service = RAGService(config)
        
        print("   正在测试路由功能...")
        test_questions = [
            "什么是机器学习？",
            "今天的天气怎么样？",
            "Python如何处理异常？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"   测试问题 {i}: {question}")
            
            try:
                start_time = time.time()
                
                # 使用超时包装
                with ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        rag_service.question_router.invoke, 
                        {"question": question}
                    )
                    
                    try:
                        result = future.result(timeout=15)  # 15秒超时
                        end_time = time.time()
                        
                        print(f"     ✅ 路由结果: {result.datasource} (耗时: {end_time - start_time:.2f}秒)")
                        
                    except TimeoutError:
                        print(f"     ❌ 路由超时 (>15秒)")
                        return False
                        
            except Exception as e:
                print(f"     ❌ 路由失败: {str(e)}")
                return False
        
        print("✅ 问题路由器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 问题路由器测试失败: {str(e)}")
        return False

def test_simple_chat():
    """测试简单聊天功能"""
    print("\n💬 测试简单聊天功能...")
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # 测试健康检查
        print("   检查服务状态...")
        health_response = requests.get(f"{base_url}/health", timeout=5)
        
        if health_response.status_code != 200:
            print(f"❌ 服务状态异常: {health_response.status_code}")
            return False
        
        print("✅ 服务状态正常")
        
        # 测试简单聊天
        print("   测试聊天API...")
        chat_data = {
            "message": "Hello, please respond with just 'OK'",
            "stream": False
        }
        
        start_time = time.time()
        chat_response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        end_time = time.time()
        
        if chat_response.status_code == 200:
            result = chat_response.json()
            print(f"✅ 聊天API成功 (耗时: {end_time - start_time:.2f}秒)")
            print(f"   响应: {result.get('response', '')[:100]}...")
            return True
        else:
            print(f"❌ 聊天API失败: {chat_response.status_code}")
            print(f"   错误: {chat_response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到RAG服务")
        print("   请确保服务正在运行: python main.py")
        return False
    except Exception as e:
        print(f"❌ 聊天测试失败: {str(e)}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    import socket
    
    # 测试DNS解析
    try:
        socket.gethostbyname("api.openai.com")
        print("✅ OpenAI DNS解析正常")
    except Exception as e:
        print(f"❌ OpenAI DNS解析失败: {e}")
        return False
    
    # 测试HTTPS连接
    try:
        import requests
        response = requests.get("https://api.openai.com/v1/models", timeout=10)
        if response.status_code in [200, 401]:  # 401是预期的（无API密钥）
            print("✅ OpenAI HTTPS连接正常")
        else:
            print(f"⚠️  OpenAI连接异常: {response.status_code}")
    except Exception as e:
        print(f"❌ OpenAI HTTPS连接失败: {e}")
        return False
    
    return True

def check_system_resources():
    """检查系统资源"""
    print("\n💻 检查系统资源...")
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        print(f"   内存使用率: {memory.percent}%")
        
        # 磁盘使用率
        disk = psutil.disk_usage('.')
        print(f"   磁盘使用率: {disk.percent}%")
        
        # 检查是否有资源瓶颈
        if cpu_percent > 90:
            print("⚠️  CPU使用率过高")
        if memory.percent > 90:
            print("⚠️  内存使用率过高")
        if disk.percent > 90:
            print("⚠️  磁盘使用率过高")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统资源检查失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🔧 RAG服务路由问题诊断")
    print("=" * 50)
    
    # 运行诊断测试
    tests = [
        ("环境变量检查", check_environment),
        ("网络连接测试", test_network_connectivity),
        ("系统资源检查", check_system_resources),
        ("OpenAI连接测试", test_openai_connection),
        ("问题路由器测试", test_question_router),
        ("聊天功能测试", test_simple_chat),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    # 提供建议
    if passed < len(results):
        print("\n🔧 建议的解决方案:")
        
        if not results[0][1]:  # 环境变量
            print("   1. 设置必需的API密钥")
            print("      export OPENAI_API_KEY=your_key")
            print("      export TAVILY_API_KEY=your_key")
        
        if not results[1][1]:  # 网络连接
            print("   2. 检查网络连接和防火墙设置")
            print("      确保可以访问 api.openai.com")
        
        if not results[3][1]:  # OpenAI连接
            print("   3. 验证OpenAI API密钥有效性")
            print("      检查API配额和权限")
        
        if not results[4][1]:  # 问题路由器
            print("   4. 重启RAG服务")
            print("      python main.py")
        
        print("\n   5. 如果问题持续，尝试:")
        print("      - 清理ChromaDB数据: rm -rf chroma_db")
        print("      - 重新安装依赖: pip install -r requirements.txt")
        print("      - 检查日志文件获取详细错误信息")
    else:
        print("\n🎉 所有测试通过！路由功能应该正常工作")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
