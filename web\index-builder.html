<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG 索引构建器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .status-panel {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .status-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .status-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .example-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .example-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .example-section pre {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            font-size: 12px;
            overflow-x: auto;
        }

        .nav-links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .nav-links a {
            color: #4facfe;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 RAG 索引构建器</h1>
            <p>构建和管理您的知识库索引</p>
        </div>

        <div class="content">
            <form id="indexForm">
                <div class="form-section">
                    <h2>📄 URL 索引</h2>
                    <div class="form-group">
                        <label for="urls">网页URL列表</label>
                        <textarea 
                            id="urls" 
                            name="urls" 
                            placeholder="请输入要索引的网页URL，每行一个：&#10;https://example.com/page1&#10;https://example.com/page2&#10;https://docs.example.com/guide"
                        ></textarea>
                        <div class="help-text">每行输入一个URL，支持HTTP和HTTPS协议</div>
                    </div>
                </div>

                <div class="form-section">
                    <h2>📝 文档索引</h2>
                    <div class="form-group">
                        <label for="documents">文档内容列表</label>
                        <textarea 
                            id="documents" 
                            name="documents" 
                            placeholder="请输入要索引的文档内容，每个文档用 --- 分隔：&#10;&#10;这是第一个文档的内容...&#10;&#10;---&#10;&#10;这是第二个文档的内容..."
                        ></textarea>
                        <div class="help-text">使用 "---" 分隔不同的文档内容</div>
                    </div>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary" id="buildBtn">
                        🚀 开始构建索引
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">
                        🗑️ 清空表单
                    </button>
                </div>
            </form>

            <div id="statusPanel" class="status-panel">
                <div id="statusContent"></div>
            </div>

            <div class="example-section">
                <h3>💡 使用示例</h3>
                <p><strong>URL示例：</strong></p>
                <pre>https://docs.python.org/3/tutorial/
https://fastapi.tiangolo.com/tutorial/
https://langchain.readthedocs.io/en/latest/</pre>
                
                <p><strong>文档内容示例：</strong></p>
                <pre>Python是一种高级编程语言，具有简洁的语法和强大的功能。

---

FastAPI是一个现代、快速的Web框架，用于构建API。

---

LangChain是一个用于开发由语言模型驱动的应用程序的框架。</pre>
            </div>

            <div class="nav-links">
                <a href="/">🏠 返回首页</a>
                <a href="/web/index.html">💬 对话界面</a>
                <a href="/docs">📚 API文档</a>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        
        document.getElementById('indexForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await buildIndex();
        });

        async function buildIndex() {
            const buildBtn = document.getElementById('buildBtn');
            const statusPanel = document.getElementById('statusPanel');
            const statusContent = document.getElementById('statusContent');
            
            // 获取表单数据
            const urlsText = document.getElementById('urls').value.trim();
            const documentsText = document.getElementById('documents').value.trim();
            
            // 解析URLs
            const urls = urlsText ? urlsText.split('\n').map(url => url.trim()).filter(url => url) : null;
            
            // 解析文档内容
            const documents = documentsText ? 
                documentsText.split('---').map(doc => doc.trim()).filter(doc => doc) : null;
            
            // 验证输入
            if (!urls && !documents) {
                showStatus('error', '请至少提供URL列表或文档内容中的一项');
                return;
            }
            
            // 准备请求数据
            const requestData = {};
            if (urls && urls.length > 0) {
                requestData.urls = urls;
            }
            if (documents && documents.length > 0) {
                requestData.documents = documents;
            }
            
            // 显示加载状态
            buildBtn.disabled = true;
            buildBtn.innerHTML = '<span class="loading"></span>构建中...';
            showStatus('info', '正在提交索引构建请求...');
            
            try {
                const response = await fetch(`${API_BASE}/index/build`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    const message = `
                        <strong>✅ ${result.message}</strong><br>
                        状态: ${result.status}<br>
                        URL数量: ${result.urls_count || 0}<br>
                        文档数量: ${result.documents_count || 0}<br>
                        <small>索引构建正在后台进行，请稍后查看构建结果</small>
                    `;
                    showStatus('success', message);
                } else {
                    showStatus('error', `构建失败: ${result.detail || '未知错误'}`);
                }
            } catch (error) {
                console.error('构建索引时出错:', error);
                showStatus('error', `网络错误: ${error.message}`);
            } finally {
                buildBtn.disabled = false;
                buildBtn.innerHTML = '🚀 开始构建索引';
            }
        }
        
        function showStatus(type, message) {
            const statusPanel = document.getElementById('statusPanel');
            const statusContent = document.getElementById('statusContent');
            
            statusPanel.className = `status-panel status-${type}`;
            statusContent.innerHTML = message;
            statusPanel.style.display = 'block';
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusPanel.style.display = 'none';
                }, 10000);
            }
        }
        
        function clearForm() {
            document.getElementById('urls').value = '';
            document.getElementById('documents').value = '';
            document.getElementById('statusPanel').style.display = 'none';
        }
        
        // 页面加载时检查服务状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (!response.ok) {
                    showStatus('error', '⚠️ RAG服务连接失败，请检查服务是否正常运行');
                }
            } catch (error) {
                showStatus('error', '⚠️ 无法连接到RAG服务，请检查服务是否正常运行');
            }
        });
    </script>
</body>
</html>
