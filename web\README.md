# RAG AI 对话界面

## 🌐 访问地址

启动服务后，您可以通过以下地址访问对话界面：

### 本地访问
- **对话界面**: http://localhost:8000/web/index.html
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

### 网络访问（如果服务器允许外部访问）
- **对话界面**: http://您的服务器IP:8000/web/index.html
- **API文档**: http://您的服务器IP:8000/docs

## 🚀 启动服务

### 方法一：使用专用启动脚本（推荐）
```bash
python start_with_web.py
```

### 方法二：使用标准启动
```bash
python main.py
```

### 方法三：使用简化启动
```bash
python simple_start.py
```

## 💬 使用说明

1. **打开对话界面**: 在浏览器中访问 http://localhost:8000/web/index.html
2. **输入问题**: 在底部输入框中输入您的问题
3. **发送消息**: 点击发送按钮或按回车键
4. **查看回复**: AI助手会智能回答您的问题

## 🎯 功能特性

- **智能对话**: 基于RAG技术的智能问答
- **实时响应**: 支持实时对话交互
- **美观界面**: 现代化的聊天界面设计
- **状态指示**: 实时显示服务连接状态
- **错误处理**: 友好的错误提示信息
- **响应式设计**: 支持手机和桌面访问

## 🔧 故障排除

### 1. 无法访问页面
- 确保服务正在运行
- 检查端口8000是否被占用
- 确认防火墙设置

### 2. 服务状态显示离线
- 检查后端API服务是否正常启动
- 确认API密钥是否正确设置
- 查看控制台错误信息

### 3. 无法发送消息
- 检查网络连接
- 确认API服务正常运行
- 查看浏览器开发者工具的错误信息

## 📱 移动端访问

该界面支持移动设备访问，您可以在手机浏览器中打开相同的地址进行对话。

## 🎨 界面预览

- **顶部**: 显示服务标题和状态指示器
- **中间**: 对话消息区域，支持滚动查看历史消息
- **底部**: 消息输入框和发送按钮
- **样式**: 现代化渐变设计，支持深浅色主题

## 🔗 相关链接

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **性能监控**: http://localhost:8000/metrics
- **项目文档**: ../README.md
