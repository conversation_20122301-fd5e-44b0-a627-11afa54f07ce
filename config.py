"""
配置管理模块
支持环境变量和配置文件
"""

import os
from typing import List, Optional
from dotenv import load_dotenv

try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    try:
        from pydantic import BaseSettings, Field
    except ImportError:
        print("请安装 pydantic-settings: pip install pydantic-settings")
        raise

# 加载环境变量
load_dotenv()

# 设置默认USER_AGENT（如果未设置）
if not os.getenv("USER_AGENT"):
    os.environ["USER_AGENT"] = "RAG-API/1.0.0 (https://github.com/your-repo/rag-api)"

# 禁用ChromaDB遥测以避免错误
os.environ["ANONYMIZED_TELEMETRY"] = "False"


class RAGConfig(BaseSettings):
    """RAG系统配置"""

    # API密钥
    openai_api_key: str = Field(
        default="",
        description="OpenAI API密钥",
        validation_alias="OPENAI_API_KEY"
    )

    tavily_api_key: str = Field(
        default="",
        description="Tavily API密钥",
        validation_alias="TAVILY_API_KEY"
    )

    # 模型配置
    model_name: str = Field(
        default="qwen3:0.6b",
        description="使用的OpenAI模型名称",
        validation_alias="MODEL_NAME"
    )

    temperature: float = Field(
        default=0.0,
        description="模型温度参数",
        validation_alias="TEMPERATURE"
    )

    # 文档处理配置
    chunk_size: int = Field(
        default=500,
        description="文档分块大小",
        validation_alias="CHUNK_SIZE"
    )

    chunk_overlap: int = Field(
        default=0,
        description="文档分块重叠大小",
        validation_alias="CHUNK_OVERLAP"
    )

    # 检索配置
    retrieval_k: int = Field(
        default=4,
        description="检索返回的文档数量",
        validation_alias="RETRIEVAL_K"
    )

    web_search_k: int = Field(
        default=3,
        description="网络搜索返回的结果数量",
        validation_alias="WEB_SEARCH_K"
    )

    # 默认索引URL
    default_urls: List[str] = Field(
        default=[
            "https://lilianweng.github.io/posts/2023-06-23-agent/",
            "https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/",
            "https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/",
        ],
        description="默认索引URL列表"
    )

    # 向量数据库配置
    vectorstore_collection_name: str = Field(
        default="rag-chroma",
        description="向量数据库集合名称",
        validation_alias="VECTORSTORE_COLLECTION_NAME"
    )

    vectorstore_persist_directory: Optional[str] = Field(
        default="./chroma_db",
        description="向量数据库持久化目录",
        validation_alias="VECTORSTORE_PERSIST_DIR"
    )

    # 添加Ollama配置
    use_local_model: bool = Field(
        default=False,
        description="是否使用本地模型",
        validation_alias="USE_LOCAL_MODEL"
    )

    ollama_base_url: str = Field(
        default="http://localhost:11434",
        description="Ollama服务地址",
        validation_alias="OLLAMA_BASE_URL"
    )

    ollama_model_name: str = Field(
        default="qwen3:0.6b",
        description="Ollama模型名称",
        validation_alias="OLLAMA_MODEL_NAME"
    )

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


class ServerConfig(BaseSettings):
    """服务器配置"""

    # 服务器配置
    host: str = Field(
        default="0.0.0.0",
        description="服务器主机地址",
        validation_alias="HOST"
    )

    port: int = Field(
        default=8000,
        description="服务器端口",
        validation_alias="PORT"
    )

    reload: bool = Field(
        default=True,
        description="是否启用热重载（开发模式）",
        validation_alias="RELOAD"
    )

    # 日志配置
    log_level: str = Field(
        default="info",
        description="日志级别",
        validation_alias="LOG_LEVEL"
    )

    # CORS配置
    cors_origins: List[str] = Field(
        default=["*"],
        description="允许的CORS源",
        validation_alias="CORS_ORIGINS"
    )

    cors_allow_credentials: bool = Field(
        default=True,
        description="是否允许CORS凭证",
        validation_alias="CORS_ALLOW_CREDENTIALS"
    )

    # API配置
    api_title: str = Field(
        default="RAG对话API",
        description="API标题",
        validation_alias="API_TITLE"
    )

    api_description: str = Field(
        default="基于LangGraph的智能RAG对话系统",
        description="API描述",
        validation_alias="API_DESCRIPTION"
    )

    api_version: str = Field(
        default="1.0.0",
        description="API版本",
        validation_alias="API_VERSION"
    )

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }


# 创建全局配置实例
rag_config = RAGConfig()
server_config = ServerConfig()


def get_rag_config() -> RAGConfig:
    """获取RAG配置"""
    return rag_config


def get_server_config() -> ServerConfig:
    """获取服务器配置"""
    return server_config


def validate_config():
    """验证配置"""
    errors = []

    # 检查必需的API密钥
    if not rag_config.openai_api_key:
        errors.append("OPENAI_API_KEY 未设置")

    if not rag_config.tavily_api_key:
        errors.append("TAVILY_API_KEY 未设置")

    # 检查数值配置
    if rag_config.chunk_size <= 0:
        errors.append("chunk_size 必须大于0")

    if rag_config.chunk_overlap < 0:
        errors.append("chunk_overlap 不能小于0")

    if rag_config.retrieval_k <= 0:
        errors.append("retrieval_k 必须大于0")

    if rag_config.web_search_k <= 0:
        errors.append("web_search_k 必须大于0")

    if not (0 <= rag_config.temperature <= 2):
        errors.append("temperature 必须在0-2之间")

    # 检查服务器配置
    if not (1 <= server_config.port <= 65535):
        errors.append("port 必须在1-65535之间")

    if server_config.log_level.lower() not in ["debug", "info", "warning", "error", "critical"]:
        errors.append("log_level 必须是有效的日志级别")

    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))

    return True


def print_config():
    """打印当前配置（隐藏敏感信息）"""
    print("=== RAG系统配置 ===")
    print(f"模型名称: {rag_config.model_name}")
    print(f"温度参数: {rag_config.temperature}")
    print(f"分块大小: {rag_config.chunk_size}")
    print(f"分块重叠: {rag_config.chunk_overlap}")
    print(f"检索数量: {rag_config.retrieval_k}")
    print(f"网搜数量: {rag_config.web_search_k}")
    print(f"向量集合: {rag_config.vectorstore_collection_name}")

    print("\n=== 服务器配置 ===")
    print(f"主机地址: {server_config.host}")
    print(f"端口号: {server_config.port}")
    print(f"热重载: {server_config.reload}")
    print(f"日志级别: {server_config.log_level}")
    print(f"API标题: {server_config.api_title}")
    print(f"API版本: {server_config.api_version}")

    print("\n=== API密钥状态 ===")
    print(f"OpenAI API: {'✓ 已设置' if rag_config.openai_api_key else '✗ 未设置'}")
    print(f"Tavily API: {'✓ 已设置' if rag_config.tavily_api_key else '✗ 未设置'}")


if __name__ == "__main__":
    # 测试配置
    try:
        validate_config()
        print_config()
        print("\n✓ 配置验证通过")
    except ValueError as e:
        print(f"\n✗ 配置验证失败: {e}")




