#!/usr/bin/env python3
"""
诊断Ollama服务的脚本
"""

import os
import sys
import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor, TimeoutError

def check_ollama_service():
    """检查Ollama服务状态"""
    print("🔍 检查Ollama服务状态...")
    
    ollama_url = "http://localhost:11434"
    
    try:
        # 检查服务是否运行
        response = requests.get(f"{ollama_url}/api/version", timeout=5)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Ollama服务运行正常")
            print(f"   版本: {version_info.get('version', 'unknown')}")
            return True
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Ollama服务未运行")
        print("   请启动Ollama服务: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Ollama服务检查失败: {e}")
        return False

def check_ollama_models():
    """检查Ollama模型"""
    print("\n📦 检查Ollama模型...")
    
    ollama_url = "http://localhost:11434"
    target_model = "qwen3:0.6b"
    
    try:
        # 获取已安装的模型列表
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models_info = response.json()
            models = models_info.get('models', [])
            
            print(f"   已安装模型数量: {len(models)}")
            
            # 检查目标模型是否存在
            target_found = False
            for model in models:
                model_name = model.get('name', '')
                model_size = model.get('size', 0)
                print(f"   - {model_name} ({model_size / 1024 / 1024 / 1024:.1f}GB)")
                
                if target_model in model_name:
                    target_found = True
                    print(f"     ✅ 找到目标模型: {model_name}")
            
            if not target_found:
                print(f"❌ 未找到目标模型: {target_model}")
                print(f"   请安装模型: ollama pull {target_model}")
                return False
            
            return True
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 模型检查失败: {e}")
        return False

def test_ollama_performance():
    """测试Ollama性能"""
    print("\n⚡ 测试Ollama性能...")
    
    ollama_url = "http://localhost:11434"
    model_name = "qwen3:0.6b"
    
    test_prompts = [
        "Hello, respond with just 'OK'",
        "What is 2+2?",
        "Explain machine learning in one sentence."
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"   测试 {i}: {prompt}")
        
        try:
            start_time = time.time()
            
            # 发送请求到Ollama
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False
            }
            
            response = requests.post(
                f"{ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                print(f"     ✅ 响应成功 (耗时: {end_time - start_time:.2f}秒)")
                print(f"     响应: {response_text[:50]}...")
            else:
                print(f"     ❌ 响应失败: {response.status_code}")
                print(f"     错误: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print(f"     ❌ 响应超时 (>30秒)")
            return False
        except Exception as e:
            print(f"     ❌ 请求失败: {e}")
            return False
    
    return True

def test_langchain_ollama():
    """测试LangChain与Ollama的集成"""
    print("\n🔗 测试LangChain与Ollama集成...")
    
    try:
        from langchain_community.llms import Ollama
        
        # 创建Ollama实例
        llm = Ollama(
            base_url="http://localhost:11434",
            model="qwen3:0.6b",
            temperature=0.1
        )
        
        test_questions = [
            "Hello",
            "What is AI?",
            "Explain Python in one sentence"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"   测试 {i}: {question}")
            
            try:
                start_time = time.time()
                
                # 使用超时包装
                with ThreadPoolExecutor() as executor:
                    future = executor.submit(llm.invoke, question)
                    
                    try:
                        response = future.result(timeout=20)  # 20秒超时
                        end_time = time.time()
                        
                        print(f"     ✅ LangChain调用成功 (耗时: {end_time - start_time:.2f}秒)")
                        print(f"     响应: {response[:50]}...")
                        
                    except TimeoutError:
                        print(f"     ❌ LangChain调用超时 (>20秒)")
                        return False
                        
            except Exception as e:
                print(f"     ❌ LangChain调用失败: {e}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ LangChain导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ LangChain测试失败: {e}")
        return False

def optimize_ollama_config():
    """优化Ollama配置建议"""
    print("\n🔧 Ollama优化建议...")
    
    print("   性能优化:")
    print("   1. 确保有足够的内存 (推荐8GB+)")
    print("   2. 使用SSD存储模型文件")
    print("   3. 关闭不必要的后台程序")
    
    print("\n   配置优化:")
    print("   1. 设置环境变量:")
    print("      export OLLAMA_NUM_PARALLEL=1")
    print("      export OLLAMA_MAX_LOADED_MODELS=1")
    print("      export OLLAMA_FLASH_ATTENTION=1")
    
    print("\n   模型选择:")
    print("   1. 对于快速响应，考虑更小的模型:")
    print("      - qwen2:0.5b (更快)")
    print("      - llama3.2:1b (平衡)")
    print("      - qwen3:0.6b (当前)")
    
    print("\n   服务配置:")
    print("   1. 增加Ollama服务的内存限制")
    print("   2. 配置合适的并发数")

def fix_rag_service_timeout():
    """修复RAG服务超时问题"""
    print("\n🛠️ 修复RAG服务超时...")
    
    # 检查当前的超时配置
    print("   当前配置检查:")
    print("   - Ollama模型: qwen3:0.6b")
    print("   - 基础URL: http://localhost:11434")
    
    print("\n   建议的修复:")
    print("   1. 增加Ollama LLM的超时时间")
    print("   2. 添加重试机制")
    print("   3. 优化路由逻辑")
    
    return True

def main():
    """主诊断函数"""
    print("🚀 Ollama服务诊断和优化")
    print("=" * 50)
    
    # 运行诊断测试
    tests = [
        ("Ollama服务检查", check_ollama_service),
        ("Ollama模型检查", check_ollama_models),
        ("Ollama性能测试", test_ollama_performance),
        ("LangChain集成测试", test_langchain_ollama),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    # 提供解决方案
    if passed < len(results):
        print("\n🔧 解决方案:")
        
        if not results[0][1]:  # Ollama服务
            print("   1. 启动Ollama服务:")
            print("      ollama serve")
        
        if not results[1][1]:  # 模型
            print("   2. 安装所需模型:")
            print("      ollama pull qwen3:0.6b")
        
        if not results[2][1] or not results[3][1]:  # 性能问题
            print("   3. 性能优化:")
            print("      - 重启Ollama服务")
            print("      - 检查系统资源")
            print("      - 考虑使用更小的模型")
    
    # 显示优化建议
    optimize_ollama_config()
    fix_rag_service_timeout()
    
    if passed == len(results):
        print("\n🎉 Ollama服务正常！可以继续使用RAG系统")
    else:
        print("\n⚠️  请按照建议修复问题后重新测试")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
