#!/usr/bin/env python3
"""
依赖修复脚本
解决常见的依赖问题
"""

import subprocess
import sys
import os


def run_command(command, description):
    """运行命令"""
    print(f"正在执行: {description}")
    print(f"命令: {' '.join(command)}")

    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        print("✅ 成功")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ 命令未找到: {command[0]}")
        return False


def fix_pydantic_settings():
    """修复pydantic-settings问题"""
    print("\n=== 修复 Pydantic Settings 问题 ===")

    # 尝试安装pydantic-settings
    commands = [
        (["python", "-m", "pip", "install", "pydantic-settings>=2.1.0"], "安装 pydantic-settings"),
        (["python", "-m", "pip", "install", "--upgrade", "pydantic>=2.5.0"], "升级 pydantic"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            return False

    return True


def fix_user_agent():
    """修复USER_AGENT问题"""
    print("\n=== 修复 USER_AGENT 问题 ===")

    # 设置环境变量
    os.environ["USER_AGENT"] = "RAG-API/1.0.0"
    print("✅ 已设置 USER_AGENT 环境变量")

    # 检查.env文件
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            print("📋 复制 .env.example 到 .env")
            try:
                with open(".env.example", "r", encoding="utf-8") as f:
                    content = f.read()
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(content)
                print("✅ .env 文件已创建")
            except Exception as e:
                print(f"❌ 创建 .env 文件失败: {e}")
                return False
        else:
            print("⚠️  .env.example 文件不存在，手动创建 .env 文件")
            env_content = """# API密钥配置
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# HTTP请求配置
USER_AGENT=RAG-API/1.0.0

# 模型配置
MODEL_NAME=gpt-4o-mini
TEMPERATURE=0.0

# 服务器配置
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=info
"""
            try:
                with open(".env", "w", encoding="utf-8") as f:
                    f.write(env_content)
                print("✅ .env 文件已创建")
            except Exception as e:
                print(f"❌ 创建 .env 文件失败: {e}")
                return False
    else:
        print("✅ .env 文件已存在")

    return True


def install_core_dependencies():
    """安装核心依赖"""
    print("\n=== 安装核心依赖 ===")

    # 核心依赖列表
    core_deps = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "python-dotenv>=1.0.0",
        "langchain>=0.1.0",
        "langchain-core>=0.1.0",
        "langchain-community>=0.0.10",
        "langchain-openai>=0.0.5",
        "langgraph>=0.0.20",
        "chromadb>=0.4.0",
        "openai>=1.0.0",
        "tavily-python>=0.3.0",
        "tiktoken>=0.5.0",
        "psutil>=5.9.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "requests>=2.31.0",
        "typing-extensions>=4.0.0",
    ]

    for dep in core_deps:
        command = ["python", "-m", "pip", "install", dep]
        if not run_command(command, f"安装 {dep}"):
            print(f"⚠️  {dep} 安装失败，继续安装其他依赖")

    return True


def test_imports():
    """测试关键导入"""
    print("\n=== 测试关键导入 ===")

    test_imports = [
        ("fastapi", "FastAPI"),
        ("pydantic", "Field"),
        ("pydantic_settings", "BaseSettings"),
        ("langchain", "LangChain"),
        ("openai", "OpenAI"),
        ("dotenv", "python-dotenv"),
    ]

    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} 导入失败: {e}")

    print(f"\n导入测试结果: {success_count}/{len(test_imports)} 成功")
    return success_count == len(test_imports)


def main():
    """主函数"""
    print("RAG API 依赖修复工具")
    print("=" * 50)

    success = True

    # 1. 修复pydantic-settings
    if not fix_pydantic_settings():
        success = False

    # 2. 修复USER_AGENT
    if not fix_user_agent():
        success = False

    # 3. 安装核心依赖
    if not install_core_dependencies():
        success = False

    # 4. 测试导入
    if not test_imports():
        success = False

    print("\n" + "=" * 50)
    if success:
        print("✅ 所有问题已修复！")
        print("\n现在可以运行:")
        print("python main.py")
    else:
        print("❌ 部分问题未能修复")
        print("\n请手动检查以下内容:")
        print("1. Python版本 >= 3.8")
        print("2. pip 版本是最新的")
        print("3. 网络连接正常")
        print("4. .env 文件中的API密钥已正确设置")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
