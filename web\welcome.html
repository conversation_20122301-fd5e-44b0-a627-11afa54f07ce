<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG AI 对话助手 - 欢迎</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .welcome-container {
            max-width: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            text-align: center;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px 20px;
        }

        .description {
            font-size: 18px;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .mode-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .mode-card {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            color: inherit;
        }

        .mode-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .mode-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .mode-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .mode-description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        .features {
            background: #f8f9fa;
            padding: 30px 20px;
            margin: 0 -20px -20px -20px;
        }

        .features h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #666;
        }

        .feature-icon {
            color: #4CAF50;
            font-size: 16px;
        }

        .api-links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .api-links h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .link-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .api-link {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 12px;
            transition: background 0.3s;
        }

        .api-link:hover {
            background: #5a67d8;
        }

        @media (max-width: 768px) {
            .mode-selection {
                grid-template-columns: 1fr;
            }

            .feature-list {
                grid-template-columns: 1fr;
            }

            .link-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="header">
            <h1>🤖 RAG AI 对话助手</h1>
            <p>基于LangGraph的智能检索增强生成系统</p>
        </div>

        <div class="content">
            <div class="description">
                选择您喜欢的对话模式，开始与AI助手的智能对话
            </div>

            <div class="mode-selection">
                <a href="index.html" class="mode-card">
                    <div class="mode-icon">💬</div>
                    <div class="mode-title">标准对话</div>
                    <div class="mode-description">
                        传统的问答模式<br>
                        完整回复，稳定可靠
                    </div>
                </a>

                <a href="stream.html" class="mode-card">
                    <div class="mode-icon">⚡</div>
                    <div class="mode-title">流式对话</div>
                    <div class="mode-description">
                        实时生成回复<br>
                        更自然的对话体验
                    </div>
                </a>

                <a href="index-builder.html" class="mode-card">
                    <div class="mode-icon">🔍</div>
                    <div class="mode-title">索引构建器</div>
                    <div class="mode-description">
                        构建知识库索引<br>
                        管理文档和URL
                    </div>
                </a>
            </div>
        </div>

        <div class="features">
            <h3>🌟 功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>智能文档检索</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>网络信息搜索</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>幻觉检测机制</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>答案质量评估</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>查询自动优化</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>响应式设计</span>
                </div>
            </div>

            <div class="api-links">
                <h4>🔗 相关链接</h4>
                <div class="link-grid">
                    <a href="/docs" class="api-link" target="_blank">📚 API文档</a>
                    <a href="/health" class="api-link" target="_blank">💚 健康检查</a>
                    <a href="/metrics" class="api-link" target="_blank">📊 性能监控</a>
                    <a href="README.md" class="api-link" target="_blank">📖 使用说明</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch('/health');
                if (response.ok) {
                    console.log('✅ RAG服务正常运行');
                } else {
                    console.warn('⚠️ RAG服务状态异常');
                }
            } catch (error) {
                console.error('❌ 无法连接到RAG服务');
            }
        }

        // 页面加载时检查服务状态
        document.addEventListener('DOMContentLoaded', () => {
            checkServiceStatus();
        });
    </script>
</body>
</html>
