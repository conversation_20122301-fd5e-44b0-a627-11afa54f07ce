#!/usr/bin/env python3
"""
简化启动脚本
避免复杂依赖问题的快速启动方案
"""

import os
import sys
from pathlib import Path

# 设置环境变量
os.environ["USER_AGENT"] = "RAG-API/1.0.0"

# 加载.env文件（简单实现）
def load_env_file():
    """简单的.env文件加载"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print("✅ .env 文件已加载")
    else:
        print("⚠️  .env 文件不存在，使用默认配置")

# 检查必要的依赖
def check_dependencies():
    """检查必要的依赖"""
    required_modules = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'langchain',
        'openai'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"❌ 缺少依赖: {', '.join(missing)}")
        print("请运行: python fix_dependencies.py")
        return False
    
    print("✅ 所有必要依赖已安装")
    return True

def main():
    """主函数"""
    print("RAG API 简化启动器")
    print("=" * 30)
    
    # 加载环境变量
    load_env_file()
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先修复依赖问题:")
        print("python fix_dependencies.py")
        return 1
    
    # 检查API密钥
    openai_key = os.getenv("OPENAI_API_KEY")
    tavily_key = os.getenv("TAVILY_API_KEY")
    
    if not openai_key or openai_key == "your_openai_api_key_here":
        print("⚠️  OPENAI_API_KEY 未设置或使用默认值")
        print("请在 .env 文件中设置正确的 OpenAI API 密钥")
    
    if not tavily_key or tavily_key == "your_tavily_api_key_here":
        print("⚠️  TAVILY_API_KEY 未设置或使用默认值")
        print("请在 .env 文件中设置正确的 Tavily API 密钥")
    
    # 启动应用
    try:
        print("\n🚀 启动 RAG API 服务...")
        
        # 导入并启动应用
        import uvicorn
        
        # 设置默认配置
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", "8000"))
        log_level = os.getenv("LOG_LEVEL", "info")
        
        print(f"服务地址: http://{host}:{port}")
        print(f"API文档: http://{host}:{port}/docs")
        print("按 Ctrl+C 停止服务")
        
        # 启动服务
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            log_level=log_level,
            reload=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请运行: python fix_dependencies.py")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
