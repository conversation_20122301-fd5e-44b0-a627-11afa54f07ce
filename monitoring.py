"""
性能监控和指标收集模块
"""

import time
import psutil
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from datetime import datetime, timedelta


@dataclass
class RequestMetrics:
    """请求指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / max(self.total_requests, 1)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_requests / max(self.total_requests, 1)
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        return self.failed_requests / max(self.total_requests, 1)


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    disk_usage_percent: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.request_metrics = defaultdict(RequestMetrics)
        self.system_metrics_history = deque(maxlen=window_size)
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._monitoring = False
        self._monitor_thread = None
    
    def start_monitoring(self, interval: float = 30.0):
        """开始系统监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_system,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止系统监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        self.logger.info("性能监控已停止")
    
    def _monitor_system(self, interval: float):
        """系统监控循环"""
        while self._monitoring:
            try:
                metrics = self._collect_system_metrics()
                with self._lock:
                    self.system_metrics_history.append(metrics)
                time.sleep(interval)
            except Exception as e:
                self.logger.error(f"系统监控错误: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            disk_usage_percent=disk.percent
        )
    
    def record_request(self, endpoint: str, response_time: float, success: bool):
        """记录请求指标"""
        with self._lock:
            metrics = self.request_metrics[endpoint]
            metrics.total_requests += 1
            metrics.total_response_time += response_time
            metrics.response_times.append(response_time)
            
            if success:
                metrics.successful_requests += 1
            else:
                metrics.failed_requests += 1
            
            # 更新最小/最大响应时间
            metrics.min_response_time = min(metrics.min_response_time, response_time)
            metrics.max_response_time = max(metrics.max_response_time, response_time)
    
    def get_request_metrics(self, endpoint: Optional[str] = None) -> Dict[str, Any]:
        """获取请求指标"""
        with self._lock:
            if endpoint:
                metrics = self.request_metrics.get(endpoint, RequestMetrics())
                return {
                    "endpoint": endpoint,
                    "total_requests": metrics.total_requests,
                    "successful_requests": metrics.successful_requests,
                    "failed_requests": metrics.failed_requests,
                    "average_response_time": metrics.average_response_time,
                    "min_response_time": metrics.min_response_time if metrics.min_response_time != float('inf') else 0,
                    "max_response_time": metrics.max_response_time,
                    "success_rate": metrics.success_rate,
                    "error_rate": metrics.error_rate
                }
            else:
                # 返回所有端点的汇总指标
                all_metrics = {}
                for ep, metrics in self.request_metrics.items():
                    all_metrics[ep] = {
                        "total_requests": metrics.total_requests,
                        "average_response_time": metrics.average_response_time,
                        "success_rate": metrics.success_rate,
                        "error_rate": metrics.error_rate
                    }
                return all_metrics
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        with self._lock:
            if not self.system_metrics_history:
                return self._collect_system_metrics().__dict__
            
            latest = self.system_metrics_history[-1]
            
            # 计算平均值（最近10个数据点）
            recent_metrics = list(self.system_metrics_history)[-10:]
            avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            
            return {
                "current_cpu_percent": latest.cpu_percent,
                "current_memory_percent": latest.memory_percent,
                "current_memory_used_mb": latest.memory_used_mb,
                "current_disk_usage_percent": latest.disk_usage_percent,
                "average_cpu_percent": avg_cpu,
                "average_memory_percent": avg_memory,
                "timestamp": latest.timestamp.isoformat()
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        system_metrics = self.get_system_metrics()
        request_metrics = self.get_request_metrics()
        
        # 健康状态评估
        health_issues = []
        
        # CPU使用率检查
        if system_metrics.get("current_cpu_percent", 0) > 80:
            health_issues.append("CPU使用率过高")
        
        # 内存使用率检查
        if system_metrics.get("current_memory_percent", 0) > 85:
            health_issues.append("内存使用率过高")
        
        # 磁盘使用率检查
        if system_metrics.get("current_disk_usage_percent", 0) > 90:
            health_issues.append("磁盘使用率过高")
        
        # 错误率检查
        total_error_rate = 0
        total_requests = 0
        for endpoint_metrics in request_metrics.values():
            if isinstance(endpoint_metrics, dict):
                total_requests += endpoint_metrics.get("total_requests", 0)
                total_error_rate += endpoint_metrics.get("error_rate", 0) * endpoint_metrics.get("total_requests", 0)
        
        if total_requests > 0:
            overall_error_rate = total_error_rate / total_requests
            if overall_error_rate > 0.1:  # 10%错误率
                health_issues.append(f"整体错误率过高: {overall_error_rate:.2%}")
        
        return {
            "status": "healthy" if not health_issues else "warning",
            "issues": health_issues,
            "system_metrics": system_metrics,
            "request_summary": {
                "total_endpoints": len(request_metrics),
                "total_requests": total_requests,
                "overall_error_rate": total_error_rate / max(total_requests, 1)
            }
        }
    
    def reset_metrics(self):
        """重置所有指标"""
        with self._lock:
            self.request_metrics.clear()
            self.system_metrics_history.clear()
        self.logger.info("性能指标已重置")


# 全局监控器实例
performance_monitor = PerformanceMonitor()


def monitor_request(endpoint: str):
    """请求监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                response_time = time.time() - start_time
                performance_monitor.record_request(endpoint, response_time, success)
        
        return wrapper
    return decorator


class RequestTimer:
    """请求计时器上下文管理器"""
    
    def __init__(self, endpoint: str):
        self.endpoint = endpoint
        self.start_time = None
        self.success = True
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.success = False
        
        response_time = time.time() - self.start_time
        performance_monitor.record_request(self.endpoint, response_time, self.success)


def get_performance_report() -> Dict[str, Any]:
    """获取性能报告"""
    return {
        "health_status": performance_monitor.get_health_status(),
        "request_metrics": performance_monitor.get_request_metrics(),
        "system_metrics": performance_monitor.get_system_metrics(),
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    # 测试监控器
    monitor = PerformanceMonitor()
    monitor.start_monitoring(interval=5.0)
    
    # 模拟一些请求
    import random
    for i in range(10):
        endpoint = random.choice(["/chat", "/health", "/index/status"])
        response_time = random.uniform(0.1, 2.0)
        success = random.choice([True, True, True, False])  # 75%成功率
        monitor.record_request(endpoint, response_time, success)
    
    # 打印报告
    import json
    report = get_performance_report()
    print(json.dumps(report, indent=2, ensure_ascii=False))
    
    monitor.stop_monitoring()
