"""
自定义异常类
"""

from typing import Optional, Dict, Any


class RAGException(Exception):
    """RAG系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = "RAG_ERROR", details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ConfigurationError(RAGException):
    """配置错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "CONFIG_ERROR", details)


class APIKeyError(ConfigurationError):
    """API密钥错误"""
    
    def __init__(self, service: str, details: Optional[Dict[str, Any]] = None):
        message = f"{service} API密钥未设置或无效"
        super().__init__(message, details)


class VectorStoreError(RAGException):
    """向量存储错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "VECTORSTORE_ERROR", details)


class IndexNotBuiltError(VectorStoreError):
    """索引未构建错误"""
    
    def __init__(self, details: Optional[Dict[str, Any]] = None):
        message = "向量索引未构建，请先调用build_index方法"
        super().__init__(message, details)


class DocumentProcessingError(RAGException):
    """文档处理错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "DOCUMENT_ERROR", details)


class RetrievalError(RAGException):
    """检索错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "RETRIEVAL_ERROR", details)


class GenerationError(RAGException):
    """生成错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "GENERATION_ERROR", details)


class WebSearchError(RAGException):
    """网络搜索错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "WEB_SEARCH_ERROR", details)


class RateLimitError(RAGException):
    """速率限制错误"""
    
    def __init__(self, service: str, details: Optional[Dict[str, Any]] = None):
        message = f"{service} API速率限制，请稍后重试"
        super().__init__(message, "RATE_LIMIT_ERROR", details)


class TimeoutError(RAGException):
    """超时错误"""
    
    def __init__(self, operation: str, timeout: float, details: Optional[Dict[str, Any]] = None):
        message = f"{operation} 操作超时 ({timeout}秒)"
        super().__init__(message, "TIMEOUT_ERROR", details)


class ValidationError(RAGException):
    """验证错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "VALIDATION_ERROR", details)


def handle_openai_error(error: Exception) -> RAGException:
    """处理OpenAI API错误"""
    error_message = str(error)
    
    if "api_key" in error_message.lower():
        return APIKeyError("OpenAI", {"original_error": error_message})
    elif "rate_limit" in error_message.lower():
        return RateLimitError("OpenAI", {"original_error": error_message})
    elif "timeout" in error_message.lower():
        return TimeoutError("OpenAI API调用", 30.0, {"original_error": error_message})
    else:
        return GenerationError(f"OpenAI API错误: {error_message}", {"original_error": error_message})


def handle_tavily_error(error: Exception) -> RAGException:
    """处理Tavily API错误"""
    error_message = str(error)
    
    if "api_key" in error_message.lower():
        return APIKeyError("Tavily", {"original_error": error_message})
    elif "rate_limit" in error_message.lower():
        return RateLimitError("Tavily", {"original_error": error_message})
    elif "timeout" in error_message.lower():
        return TimeoutError("Tavily API调用", 30.0, {"original_error": error_message})
    else:
        return WebSearchError(f"Tavily API错误: {error_message}", {"original_error": error_message})


def handle_chroma_error(error: Exception) -> RAGException:
    """处理Chroma向量数据库错误"""
    error_message = str(error)
    
    if "collection" in error_message.lower():
        return VectorStoreError(f"向量数据库集合错误: {error_message}", {"original_error": error_message})
    elif "embedding" in error_message.lower():
        return VectorStoreError(f"嵌入向量错误: {error_message}", {"original_error": error_message})
    else:
        return VectorStoreError(f"向量数据库错误: {error_message}", {"original_error": error_message})


def handle_document_loading_error(error: Exception, url: str = None) -> RAGException:
    """处理文档加载错误"""
    error_message = str(error)
    details = {"original_error": error_message}
    
    if url:
        details["url"] = url
    
    if "timeout" in error_message.lower():
        return TimeoutError("文档加载", 30.0, details)
    elif "404" in error_message or "not found" in error_message.lower():
        return DocumentProcessingError(f"文档未找到: {url or '未知URL'}", details)
    elif "403" in error_message or "forbidden" in error_message.lower():
        return DocumentProcessingError(f"文档访问被拒绝: {url or '未知URL'}", details)
    else:
        return DocumentProcessingError(f"文档加载失败: {error_message}", details)
