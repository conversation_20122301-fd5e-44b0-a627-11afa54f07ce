"""
FastAPI后端服务
提供RAG对话API接口
"""

import os
from typing import List, Optional
from contextlib import asynccontextmanager
import logging

try:
    from fastapi import FastAPI, HTTPException, BackgroundTasks
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import StreamingResponse
    from fastapi.staticfiles import StaticFiles
    from pydantic import BaseModel, Field
    import uvicorn
except ImportError as e:
    print(f"请安装必要的依赖: pip install fastapi uvicorn")
    raise e

from rag_service import RAGService
from config import get_rag_config, get_server_config, validate_config
from exceptions import RAGException, IndexNotBuiltError, ValidationError
from logger import setup_rag_logging, request_logger
from monitoring import performance_monitor, RequestTimer, get_performance_report

# 获取配置
rag_config = get_rag_config()
server_config = get_server_config()

# 设置日志
setup_rag_logging(server_config)
logger = logging.getLogger(__name__)

# 全局RAG服务实例
rag_service: Optional[RAGService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global rag_service

    # 验证配置
    logger.info("验证配置...")
    try:
        validate_config()
        logger.info("配置验证通过")
    except ValueError as e:
        logger.error(f"配置验证失败: {e}")
        raise

    # 启动性能监控
    logger.info("启动性能监控...")
    performance_monitor.start_monitoring(interval=30.0)

    # 启动时初始化RAG服务
    logger.info("初始化RAG服务...")
    try:
        rag_service = RAGService(rag_config)
        # 构建默认索引
        # rag_service.build_index()
        logger.info("RAG服务初始化完成")
    except Exception as e:
        logger.error(f"RAG服务初始化失败: {str(e)}")
        raise

    yield

    # 关闭时清理资源
    logger.info("清理RAG服务资源...")
    performance_monitor.stop_monitoring()


# 创建FastAPI应用
app = FastAPI(
    title=server_config.api_title,
    description=server_config.api_description,
    version=server_config.api_version,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=server_config.cors_origins,
    allow_credentials=server_config.cors_allow_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
import os
if os.path.exists("web"):
    app.mount("/web", StaticFiles(directory="web"), name="web")


# 请求/响应模型
class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息", min_length=1, max_length=1000)
    stream: bool = Field(False, description="是否使用流式响应")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str = Field(..., description="AI回复")
    status: str = Field("success", description="响应状态")


class IndexRequest(BaseModel):
    """索引构建请求模型"""
    urls: Optional[List[str]] = Field(None, description="要索引的URL列表")
    documents: Optional[List[str]] = Field(None, description="要索引的文档内容列表")


class StatusResponse(BaseModel):
    """状态响应模型"""
    status: str = Field(..., description="服务状态")
    details: dict = Field(..., description="详细信息")


@app.get("/", summary="根路径")
async def root():
    """根路径 - 提供服务信息和访问链接"""
    return {
        "message": "RAG对话API服务正在运行",
        "status": "healthy",
        "chat_interface": "/web/index.html",
        "api_docs": "/docs",
        "health_check": "/health"
    }


@app.get("/health", response_model=StatusResponse, summary="服务健康状态")
async def health_check():
    """检查服务健康状态"""
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG服务未初始化")

    try:
        with RequestTimer("/health"):
            status_info = rag_service.get_status()
            health_status = performance_monitor.get_health_status()

            # 合并RAG服务状态和系统健康状态
            combined_status = {
                **status_info,
                "system_health": health_status
            }

            return StatusResponse(
                status="healthy" if health_status["status"] == "healthy" else "warning",
                details=combined_status
            )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务异常: {str(e)}")


@app.post("/chat", response_model=ChatResponse, summary="对话接口")
async def chat(request: ChatRequest):
    """
    与AI进行对话

    - **message**: 用户消息
    - **stream**: 是否使用流式响应（此接口不支持流式，请使用/chat/stream）
    """
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG服务未初始化")

    # 记录请求
    request_logger.log_request("POST", "/chat")

    try:
        with RequestTimer("/chat"):
            # 如果请求流式响应，提示使用正确的端点
            if request.stream:
                raise HTTPException(
                    status_code=400,
                    detail="此接口不支持流式响应，请使用 /chat/stream 端点"
                )

            # 调用RAG服务
            response = rag_service.query(request.message)

            # 记录成功响应
            request_logger.log_response(200, 0.0)  # 执行时间会在装饰器中记录

            return ChatResponse(response=response)

    except ValidationError as e:
        logger.warning(f"输入验证失败: {str(e)}")
        request_logger.log_error(e)
        raise HTTPException(status_code=400, detail=str(e))

    except IndexNotBuiltError as e:
        logger.error(f"索引未构建: {str(e)}")
        request_logger.log_error(e)
        raise HTTPException(status_code=503, detail="向量索引未构建，请联系管理员")

    except RAGException as e:
        logger.error(f"RAG系统错误: {str(e)}")
        request_logger.log_error(e)
        raise HTTPException(status_code=500, detail=f"处理失败: {e.message}")

    except Exception as e:
        logger.error(f"对话处理失败: {str(e)}", exc_info=True)
        request_logger.log_error(e)
        raise HTTPException(status_code=500, detail="服务内部错误，请稍后重试")


@app.post("/chat/stream", summary="流式对话接口")
async def chat_stream(request: ChatRequest):
    """
    与AI进行流式对话

    - **message**: 用户消息
    - **stream**: 此参数在流式接口中被忽略
    """
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG服务未初始化")

    # 记录请求
    request_logger.log_request("POST", "/chat/stream")

    try:
        async def generate_response():
            """生成流式响应"""
            try:
                async for chunk in rag_service.query_stream(request.message):
                    # 使用Server-Sent Events格式
                    yield f"data: {chunk}\n\n"
                yield "data: [DONE]\n\n"

                # 记录成功响应
                request_logger.log_response(200, 0.0)

            except ValidationError as e:
                logger.warning(f"流式对话输入验证失败: {str(e)}")
                request_logger.log_error(e)
                yield f"data: 错误: {str(e)}\n\n"
                yield "data: [ERROR]\n\n"

            except IndexNotBuiltError as e:
                logger.error(f"流式对话索引未构建: {str(e)}")
                request_logger.log_error(e)
                yield "data: 错误: 向量索引未构建，请联系管理员\n\n"
                yield "data: [ERROR]\n\n"

            except RAGException as e:
                logger.error(f"流式对话RAG系统错误: {str(e)}")
                request_logger.log_error(e)
                yield f"data: 错误: {e.message}\n\n"
                yield "data: [ERROR]\n\n"

            except Exception as e:
                logger.error(f"流式对话处理失败: {str(e)}", exc_info=True)
                request_logger.log_error(e)
                yield "data: 错误: 服务内部错误，请稍后重试\n\n"
                yield "data: [ERROR]\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )

    except Exception as e:
        logger.error(f"流式对话初始化失败: {str(e)}", exc_info=True)
        request_logger.log_error(e)
        raise HTTPException(status_code=500, detail="服务内部错误，请稍后重试")


@app.post("/index/build", summary="构建索引")
async def build_index(request: IndexRequest, background_tasks: BackgroundTasks):
    """
    构建或重建向量索引

    - **urls**: 要索引的URL列表（可选）
    - **documents**: 要索引的文档内容列表（可选）

    如果两个参数都为空，将使用默认的URL列表
    """
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG服务未初始化")

    def build_index_task():
        """后台任务：构建索引"""
        try:
            rag_service.build_index(urls=request.urls, documents=request.documents)
            logger.info("索引构建完成")
        except Exception as e:
            logger.error(f"后台索引构建失败: {str(e)}")

    # 添加后台任务
    background_tasks.add_task(build_index_task)

    return {
        "message": "索引构建任务已启动",
        "status": "building",
        "urls_count": len(request.urls) if request.urls else 0,
        "documents_count": len(request.documents) if request.documents else 0
    }


@app.get("/index/status", summary="索引状态")
async def index_status():
    """获取索引状态"""
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG服务未初始化")

    try:
        status_info = rag_service.get_status()
        return {
            "vectorstore_ready": status_info["vectorstore_ready"],
            "retriever_ready": status_info["retriever_ready"],
            "model_name": status_info["model_name"],
            "chunk_size": status_info["chunk_size"]
        }
    except Exception as e:
        logger.error(f"获取索引状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取索引状态失败: {str(e)}")


@app.get("/metrics", summary="性能指标")
async def get_metrics():
    """获取性能指标"""
    try:
        with RequestTimer("/metrics"):
            return get_performance_report()
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")


@app.post("/metrics/reset", summary="重置性能指标")
async def reset_metrics():
    """重置性能指标"""
    try:
        performance_monitor.reset_metrics()
        return {"message": "性能指标已重置", "status": "success"}
    except Exception as e:
        logger.error(f"重置性能指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置性能指标失败: {str(e)}")


if __name__ == "__main__":
    # 启动服务
    uvicorn.run(
        "main:app",
        host=server_config.host,
        port=server_config.port,
        reload=server_config.reload,
        log_level=server_config.log_level
    )
