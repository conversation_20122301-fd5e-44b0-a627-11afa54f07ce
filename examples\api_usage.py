#!/usr/bin/env python3
"""
API使用示例
演示如何使用RAG对话API
"""

import requests
import json
import time
import asyncio
import aiohttp
from typing import AsyncGenerator


class RAGAPIClient:
    """RAG API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> dict:
        """健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def chat(self, message: str) -> str:
        """标准对话"""
        data = {"message": message, "stream": False}
        response = self.session.post(f"{self.base_url}/chat", json=data)
        response.raise_for_status()
        return response.json()["response"]
    
    def chat_stream(self, message: str) -> str:
        """流式对话"""
        data = {"message": message}
        response = self.session.post(
            f"{self.base_url}/chat/stream", 
            json=data, 
            stream=True
        )
        response.raise_for_status()
        
        full_response = ""
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith("data: "):
                content = line[6:]  # 移除 "data: " 前缀
                if content == "[DONE]":
                    break
                elif content == "[ERROR]":
                    break
                else:
                    full_response += content
                    print(content, end="", flush=True)
        
        print()  # 换行
        return full_response
    
    def build_index(self, urls: list = None, documents: list = None) -> dict:
        """构建索引"""
        data = {}
        if urls:
            data["urls"] = urls
        if documents:
            data["documents"] = documents
        
        response = self.session.post(f"{self.base_url}/index/build", json=data)
        response.raise_for_status()
        return response.json()
    
    def index_status(self) -> dict:
        """获取索引状态"""
        response = self.session.get(f"{self.base_url}/index/status")
        response.raise_for_status()
        return response.json()


async def async_chat_stream(base_url: str, message: str) -> AsyncGenerator[str, None]:
    """异步流式对话"""
    data = {"message": message}
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{base_url}/chat/stream",
            json=data
        ) as response:
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith("data: "):
                    content = line_str[6:]
                    if content == "[DONE]":
                        break
                    elif content == "[ERROR]":
                        break
                    else:
                        yield content


def demo_basic_usage():
    """基础使用演示"""
    print("=== RAG API 基础使用演示 ===\n")
    
    client = RAGAPIClient()
    
    try:
        # 1. 健康检查
        print("1. 健康检查...")
        health = client.health_check()
        print(f"服务状态: {health['status']}")
        print(f"详细信息: {json.dumps(health['details'], indent=2, ensure_ascii=False)}")
        print()
        
        # 2. 索引状态
        print("2. 检查索引状态...")
        index_status = client.index_status()
        print(f"向量存储就绪: {index_status['vectorstore_ready']}")
        print(f"检索器就绪: {index_status['retriever_ready']}")
        print(f"模型名称: {index_status['model_name']}")
        print()
        
        # 3. 标准对话
        print("3. 标准对话演示...")
        questions = [
            "什么是agent memory?",
            "解释一下prompt engineering的概念",
            "RAG系统有什么优势?"
        ]
        
        for question in questions:
            print(f"问题: {question}")
            print("回答: ", end="")
            answer = client.chat(question)
            print(answer)
            print("-" * 50)
        
        # 4. 流式对话
        print("\n4. 流式对话演示...")
        question = "详细解释一下LangGraph的工作原理"
        print(f"问题: {question}")
        print("回答: ", end="")
        client.chat_stream(question)
        print("-" * 50)
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        print("启动命令: python main.py")
    except requests.exceptions.HTTPError as e:
        print(f"❌ API请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")


def demo_index_management():
    """索引管理演示"""
    print("=== 索引管理演示 ===\n")
    
    client = RAGAPIClient()
    
    try:
        # 1. 使用自定义文档构建索引
        print("1. 使用自定义文档构建索引...")
        custom_docs = [
            "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
            "深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。"
        ]
        
        result = client.build_index(documents=custom_docs)
        print(f"索引构建状态: {result['status']}")
        print(f"文档数量: {result['documents_count']}")
        print()
        
        # 等待索引构建完成（实际应用中可能需要轮询状态）
        print("等待索引构建完成...")
        time.sleep(5)
        
        # 2. 测试自定义索引
        print("2. 测试自定义索引...")
        question = "什么是深度学习?"
        answer = client.chat(question)
        print(f"问题: {question}")
        print(f"回答: {answer}")
        print()
        
        # 3. 使用URL构建索引
        print("3. 使用URL构建索引...")
        urls = [
            "https://en.wikipedia.org/wiki/Artificial_intelligence",
            "https://en.wikipedia.org/wiki/Machine_learning"
        ]
        
        result = client.build_index(urls=urls)
        print(f"索引构建状态: {result['status']}")
        print(f"URL数量: {result['urls_count']}")
        
    except Exception as e:
        print(f"❌ 索引管理演示失败: {e}")


async def demo_async_streaming():
    """异步流式对话演示"""
    print("=== 异步流式对话演示 ===\n")
    
    base_url = "http://localhost:8000"
    question = "请详细介绍一下RAG系统的架构和工作流程"
    
    print(f"问题: {question}")
    print("回答: ", end="")
    
    try:
        async for chunk in async_chat_stream(base_url, question):
            print(chunk, end="", flush=True)
        print()
    except Exception as e:
        print(f"\n❌ 异步流式对话失败: {e}")


def demo_error_handling():
    """错误处理演示"""
    print("=== 错误处理演示 ===\n")
    
    client = RAGAPIClient()
    
    # 1. 空消息测试
    print("1. 测试空消息...")
    try:
        client.chat("")
    except requests.exceptions.HTTPError as e:
        print(f"预期错误: {e.response.status_code} - {e.response.json()['detail']}")
    
    # 2. 过长消息测试
    print("\n2. 测试过长消息...")
    long_message = "a" * 1001
    try:
        client.chat(long_message)
    except requests.exceptions.HTTPError as e:
        print(f"预期错误: {e.response.status_code}")
    
    # 3. 无效端点测试
    print("\n3. 测试无效端点...")
    try:
        response = client.session.get(f"{client.base_url}/invalid-endpoint")
        response.raise_for_status()
    except requests.exceptions.HTTPError as e:
        print(f"预期错误: {e.response.status_code}")


def performance_test():
    """性能测试"""
    print("=== 性能测试 ===\n")
    
    client = RAGAPIClient()
    
    questions = [
        "什么是机器学习?",
        "解释神经网络的工作原理",
        "深度学习有哪些应用?",
        "什么是自然语言处理?",
        "计算机视觉的主要任务是什么?"
    ]
    
    print(f"测试 {len(questions)} 个问题的响应时间...")
    
    total_time = 0
    for i, question in enumerate(questions, 1):
        start_time = time.time()
        try:
            answer = client.chat(question)
            end_time = time.time()
            response_time = end_time - start_time
            total_time += response_time
            
            print(f"问题 {i}: {response_time:.2f}秒 - {question[:30]}...")
        except Exception as e:
            print(f"问题 {i} 失败: {e}")
    
    if total_time > 0:
        avg_time = total_time / len(questions)
        print(f"\n平均响应时间: {avg_time:.2f}秒")
        print(f"总耗时: {total_time:.2f}秒")


def main():
    """主函数"""
    print("RAG API 使用示例\n")
    
    demos = [
        ("基础使用", demo_basic_usage),
        ("索引管理", demo_index_management),
        ("错误处理", demo_error_handling),
        ("性能测试", performance_test),
    ]
    
    for name, demo_func in demos:
        print(f"\n{'='*60}")
        print(f"运行演示: {name}")
        print(f"{'='*60}")
        
        try:
            demo_func()
        except KeyboardInterrupt:
            print("\n用户中断")
            break
        except Exception as e:
            print(f"演示失败: {e}")
        
        input("\n按回车键继续下一个演示...")
    
    # 异步演示
    print(f"\n{'='*60}")
    print("运行演示: 异步流式对话")
    print(f"{'='*60}")
    
    try:
        asyncio.run(demo_async_streaming())
    except Exception as e:
        print(f"异步演示失败: {e}")
    
    print("\n所有演示完成!")


if __name__ == "__main__":
    main()
