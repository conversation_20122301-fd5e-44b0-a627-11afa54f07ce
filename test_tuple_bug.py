#!/usr/bin/env python3
"""
测试tuple bug的脚本
"""

import requests
import json

def test_chat_api():
    """测试聊天API"""
    print("🧪 测试聊天API...")
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试健康检查
        print("   检查服务状态...")
        health_response = requests.get(f"{base_url}/health", timeout=5)
        
        if health_response.status_code != 200:
            print(f"❌ 服务状态异常: {health_response.status_code}")
            return False
        
        print("✅ 服务状态正常")
        
        # 测试简单问题
        test_questions = [
            "Hello",
            "什么是机器学习？",
            "Python的基本语法有哪些？"
        ]
        
        for question in test_questions:
            print(f"\n   测试问题: {question}")
            
            try:
                chat_data = {
                    "message": question,
                    "stream": False
                }
                
                response = requests.post(
                    f"{base_url}/chat",
                    json=chat_data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"     ✅ 响应成功")
                    print(f"     响应: {result.get('response', '')[:100]}...")
                else:
                    print(f"     ❌ 响应失败: {response.status_code}")
                    print(f"     错误: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"     ❌ 请求失败: {e}")
                return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到RAG服务")
        print("   请确保服务正在运行: python main.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_rag_service_directly():
    """直接测试RAG服务"""
    print("\n🔧 直接测试RAG服务...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        rag_service = RAGService(config)
        
        if rag_service.vectorstore is None:
            print("   ⚠️  向量存储未初始化，跳过测试")
            return True
        
        # 测试检索功能
        test_question = "什么是机器学习？"
        print(f"   测试检索: {test_question}")
        
        try:
            # 测试检索
            state = {"question": test_question}
            result = rag_service.retrieve(state)
            
            documents = result["documents"]
            print(f"     ✅ 检索成功，获得 {len(documents)} 个文档")
            
            # 检查文档格式
            for i, doc in enumerate(documents):
                print(f"     文档 {i} 类型: {type(doc)}")
                if hasattr(doc, 'page_content'):
                    print(f"     文档 {i} 内容长度: {len(doc.page_content)}")
                else:
                    print(f"     ❌ 文档 {i} 没有 page_content 属性")
                    return False
            
            # 测试格式化
            print("   测试文档格式化...")
            formatted = rag_service.format_docs(documents)
            print(f"     ✅ 格式化成功，长度: {len(formatted)}")
            
            return True
            
        except Exception as e:
            print(f"     ❌ 检索测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ RAG服务测试失败: {e}")
        return False

def test_rerank_function():
    """测试重排序功能"""
    print("\n🔄 测试重排序功能...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        from langchain.schema import Document
        
        config = get_rag_config()
        rag_service = RAGService(config)
        
        # 创建测试文档
        test_docs = [
            Document(page_content="机器学习是人工智能的一个分支", metadata={"source": "doc1"}),
            Document(page_content="Python是一种编程语言", metadata={"source": "doc2"}),
            Document(page_content="深度学习是机器学习的子集", metadata={"source": "doc3"})
        ]
        
        query = "什么是机器学习？"
        
        print(f"   测试重排序: {query}")
        print(f"   输入文档数量: {len(test_docs)}")
        
        # 测试重排序
        reranked_docs = rag_service._rerank_documents(query, test_docs, top_k=2)
        
        print(f"   ✅ 重排序成功，输出文档数量: {len(reranked_docs)}")
        
        # 检查输出格式
        for i, doc in enumerate(reranked_docs):
            print(f"   文档 {i} 类型: {type(doc)}")
            if hasattr(doc, 'page_content'):
                print(f"   文档 {i} 内容: {doc.page_content[:50]}...")
            else:
                print(f"   ❌ 文档 {i} 格式错误")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 重排序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Tuple Bug 诊断测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("RAG服务直接测试", test_rag_service_directly),
        ("重排序功能测试", test_rerank_function),
        ("聊天API测试", test_chat_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过，tuple bug已修复！")
    else:
        print("\n⚠️  仍有问题需要解决")
        print("\n🔧 建议:")
        print("   1. 检查日志中的调试信息")
        print("   2. 确认重排序功能正常工作")
        print("   3. 验证文档格式一致性")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
