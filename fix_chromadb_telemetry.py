#!/usr/bin/env python3
"""
修复ChromaDB遥测错误的脚本
解决 "capture() takes 1 positional argument but 3 were given" 错误
"""

import os
import sys
import subprocess
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def set_environment_variables():
    """设置环境变量禁用遥测"""
    logger.info("设置环境变量禁用ChromaDB遥测...")
    
    # 禁用ChromaDB遥测的环境变量
    telemetry_vars = {
        "ANONYMIZED_TELEMETRY": "False",
        "CHROMA_TELEMETRY": "False",
        "CHROMA_TELEMETRY_ENABLED": "False",
        "POSTHOG_DISABLED": "True",
        "DO_NOT_TRACK": "1"
    }
    
    for var, value in telemetry_vars.items():
        os.environ[var] = value
        logger.info(f"设置 {var}={value}")
    
    return True

def check_chromadb_version():
    """检查ChromaDB版本"""
    try:
        import chromadb
        version = chromadb.__version__
        logger.info(f"ChromaDB版本: {version}")
        return version
    except ImportError:
        logger.error("ChromaDB未安装")
        return None
    except Exception as e:
        logger.error(f"检查ChromaDB版本失败: {e}")
        return None

def check_posthog_version():
    """检查posthog版本"""
    try:
        import posthog
        version = posthog.__version__
        logger.info(f"Posthog版本: {version}")
        return version
    except ImportError:
        logger.warning("Posthog未安装")
        return None
    except Exception as e:
        logger.error(f"检查Posthog版本失败: {e}")
        return None

def upgrade_chromadb():
    """升级ChromaDB到最新版本"""
    logger.info("升级ChromaDB到最新版本...")
    
    try:
        # 升级chromadb
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "chromadb"
        ], capture_output=True, text=True, check=True)
        
        logger.info("ChromaDB升级成功")
        logger.debug(f"升级输出: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"ChromaDB升级失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def downgrade_posthog():
    """降级posthog到兼容版本"""
    logger.info("降级posthog到兼容版本...")
    
    try:
        # 安装兼容的posthog版本
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "posthog==2.4.2"
        ], capture_output=True, text=True, check=True)
        
        logger.info("Posthog降级成功")
        logger.debug(f"降级输出: {result.stdout}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Posthog降级失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def test_chromadb_import():
    """测试ChromaDB导入"""
    logger.info("测试ChromaDB导入...")
    
    try:
        # 重新导入以应用环境变量
        if 'chromadb' in sys.modules:
            del sys.modules['chromadb']
        
        import chromadb
        
        # 创建一个临时客户端测试
        client = chromadb.Client()
        logger.info("ChromaDB导入和初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"ChromaDB测试失败: {e}")
        return False

def create_env_file():
    """创建或更新.env文件以包含遥测设置"""
    logger.info("更新.env文件...")
    
    env_file = ".env"
    telemetry_settings = [
        "# ChromaDB遥测设置",
        "ANONYMIZED_TELEMETRY=False",
        "CHROMA_TELEMETRY=False", 
        "CHROMA_TELEMETRY_ENABLED=False",
        "POSTHOG_DISABLED=True",
        "DO_NOT_TRACK=1",
        ""
    ]
    
    try:
        # 读取现有内容
        existing_content = ""
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        
        # 检查是否已经包含遥测设置
        if "ANONYMIZED_TELEMETRY" not in existing_content:
            with open(env_file, 'a', encoding='utf-8') as f:
                f.write("\n")
                f.write("\n".join(telemetry_settings))
            logger.info(f"已更新 {env_file} 文件")
        else:
            logger.info(f"{env_file} 文件已包含遥测设置")
        
        return True
        
    except Exception as e:
        logger.error(f"更新.env文件失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始修复ChromaDB遥测错误...")
    logger.info("=" * 50)
    
    success = True
    
    # 1. 设置环境变量
    if not set_environment_variables():
        success = False
    
    # 2. 检查版本
    chromadb_version = check_chromadb_version()
    posthog_version = check_posthog_version()
    
    # 3. 尝试升级ChromaDB
    if chromadb_version:
        if not upgrade_chromadb():
            logger.warning("ChromaDB升级失败，尝试其他解决方案")
    
    # 4. 如果有posthog且版本可能有问题，尝试降级
    if posthog_version and posthog_version.startswith("3."):
        logger.info("检测到Posthog 3.x版本，尝试降级到兼容版本")
        if not downgrade_posthog():
            logger.warning("Posthog降级失败")
    
    # 5. 测试ChromaDB
    if not test_chromadb_import():
        success = False
    
    # 6. 创建/更新.env文件
    if not create_env_file():
        success = False
    
    # 总结
    logger.info("=" * 50)
    if success:
        logger.info("✅ ChromaDB遥测错误修复完成！")
        logger.info("\n建议:")
        logger.info("1. 重启RAG服务以应用更改")
        logger.info("2. 如果仍有错误，请检查日志")
        logger.info("3. 考虑在虚拟环境中运行")
    else:
        logger.error("❌ 部分修复失败")
        logger.info("\n手动解决方案:")
        logger.info("1. 设置环境变量: export ANONYMIZED_TELEMETRY=False")
        logger.info("2. 升级ChromaDB: pip install --upgrade chromadb")
        logger.info("3. 降级Posthog: pip install posthog==2.4.2")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
