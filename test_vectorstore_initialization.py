#!/usr/bin/env python3
"""
测试向量存储初始化逻辑
"""

import os
import shutil
import time

def test_fresh_start():
    """测试全新启动（无现有数据）"""
    print("🆕 测试全新启动...")
    
    # 清理现有数据
    chroma_dir = "./chroma_db"
    if os.path.exists(chroma_dir):
        shutil.rmtree(chroma_dir)
        print(f"   清理现有数据: {chroma_dir}")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        
        print("   正在初始化RAG服务...")
        start_time = time.time()
        rag_service = RAGService(config)
        end_time = time.time()
        
        print(f"   ✅ RAG服务初始化完成 (耗时: {end_time - start_time:.2f}秒)")
        
        # 检查向量存储状态
        if rag_service.vectorstore is not None:
            collection = rag_service.vectorstore._collection
            doc_count = collection.count()
            print(f"   📊 向量数据库已创建，包含 {doc_count} 个文档")
            
            if doc_count > 0:
                print("   ✅ 默认URL已自动索引")
                return True
            else:
                print("   ⚠️  向量数据库为空")
                return False
        else:
            print("   ⚠️  向量数据库未创建")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_existing_data():
    """测试现有数据启动"""
    print("\n🔄 测试现有数据启动...")
    
    chroma_dir = "./chroma_db"
    
    # 检查是否有现有数据
    if not os.path.exists(chroma_dir) or not os.listdir(chroma_dir):
        print("   ⚠️  没有现有数据，跳过测试")
        return True
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        
        print("   正在加载现有数据...")
        start_time = time.time()
        rag_service = RAGService(config)
        end_time = time.time()
        
        print(f"   ✅ RAG服务初始化完成 (耗时: {end_time - start_time:.2f}秒)")
        
        # 检查向量存储状态
        if rag_service.vectorstore is not None:
            collection = rag_service.vectorstore._collection
            doc_count = collection.count()
            print(f"   📊 成功加载现有数据，包含 {doc_count} 个文档")
            
            if doc_count > 0:
                print("   ✅ 现有数据加载成功")
                return True
            else:
                print("   ⚠️  数据库为空")
                return False
        else:
            print("   ❌ 向量数据库加载失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_append_mode():
    """测试追加模式"""
    print("\n➕ 测试追加模式...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        rag_service = RAGService(config)
        
        if rag_service.vectorstore is None:
            print("   ⚠️  没有现有向量存储，跳过追加测试")
            return True
        
        # 获取当前文档数量
        collection = rag_service.vectorstore._collection
        original_count = collection.count()
        print(f"   原始文档数量: {original_count}")
        
        # 测试追加新文档
        test_docs = [
            "这是一个测试文档，用于验证追加功能。",
            "追加模式应该将新文档添加到现有索引中。"
        ]
        
        print("   正在追加新文档...")
        start_time = time.time()
        rag_service.build_index(documents=test_docs, append=True)
        end_time = time.time()
        
        # 检查文档数量是否增加
        new_count = collection.count()
        added_count = new_count - original_count
        
        print(f"   ✅ 追加完成 (耗时: {end_time - start_time:.2f}秒)")
        print(f"   📊 新增文档数量: {added_count}")
        print(f"   📊 总文档数量: {new_count}")
        
        return added_count > 0
        
    except Exception as e:
        print(f"   ❌ 追加测试失败: {e}")
        return False

def test_rebuild_mode():
    """测试重建模式"""
    print("\n🔄 测试重建模式...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        rag_service = RAGService(config)
        
        if rag_service.vectorstore is None:
            print("   ⚠️  没有现有向量存储，跳过重建测试")
            return True
        
        # 获取当前文档数量
        collection = rag_service.vectorstore._collection
        original_count = collection.count()
        print(f"   原始文档数量: {original_count}")
        
        # 测试重建索引
        test_docs = [
            "这是重建后的第一个文档。",
            "这是重建后的第二个文档。"
        ]
        
        print("   正在重建索引...")
        start_time = time.time()
        rag_service.build_index(documents=test_docs, append=False)
        end_time = time.time()
        
        # 检查文档数量
        new_count = collection.count()
        
        print(f"   ✅ 重建完成 (耗时: {end_time - start_time:.2f}秒)")
        print(f"   📊 重建后文档数量: {new_count}")
        
        return new_count == len(test_docs)
        
    except Exception as e:
        print(f"   ❌ 重建测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # 测试健康检查
        print("   检查服务状态...")
        health_response = requests.get(f"{base_url}/health", timeout=5)
        
        if health_response.status_code != 200:
            print(f"   ❌ 服务状态异常: {health_response.status_code}")
            return False
        
        print("   ✅ 服务状态正常")
        
        # 测试追加索引API
        print("   测试追加索引API...")
        append_data = {
            "documents": ["这是通过API追加的测试文档。"],
            "append": True
        }
        
        response = requests.post(
            f"{base_url}/index/build",
            json=append_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 追加API成功: {result.get('message', '')}")
        else:
            print(f"   ❌ 追加API失败: {response.status_code}")
            return False
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("   ⚠️  RAG服务未运行，跳过API测试")
        return True
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 向量存储初始化逻辑测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("全新启动测试", test_fresh_start),
        ("现有数据启动测试", test_existing_data),
        ("追加模式测试", test_append_mode),
        ("重建模式测试", test_rebuild_mode),
        ("API端点测试", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed >= 3:
        print("\n🎉 向量存储初始化逻辑工作正常！")
        print("\n📋 新功能:")
        print("   - 服务启动时自动检查现有数据")
        print("   - 首次启动自动使用默认URL构建索引")
        print("   - 支持追加和重建两种模式")
        print("   - API支持append参数")
        
        print("\n💡 使用建议:")
        print("   - 首次启动会自动构建索引")
        print("   - 后续启动会加载现有数据")
        print("   - 使用append=true追加新内容")
        print("   - 使用append=false重建索引")
    else:
        print("\n⚠️  部分功能需要优化")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
