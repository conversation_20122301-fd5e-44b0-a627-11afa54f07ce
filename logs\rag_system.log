2025-06-30 22:35:33,231 - main - INFO - 验证配置...
2025-06-30 22:35:33,232 - main - INFO - 配置验证通过
2025-06-30 22:35:33,233 - main - INFO - 启动性能监控...
2025-06-30 22:35:33,235 - monitoring - INFO - 性能监控已启动
2025-06-30 22:35:33,236 - main - INFO - 初始化RAG服务...
2025-06-30 22:35:50,070 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:35:50,071 - RAGService - INFO - 开始构建索引，URLs: 3, 文档: 0
2025-06-30 22:35:57,248 - RAGService - INFO - 成功加载 3 个文档
2025-06-30 22:35:57,342 - RAGService - INFO - 文档分割完成，共 88 个块
2025-06-30 22:35:57,882 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-30 22:35:57,946 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-06-30 22:35:57,947 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
2025-06-30 22:36:11,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:36:11,726 - openai._base_client - INFO - Retrying request to /embeddings in 0.421586 seconds
2025-06-30 22:36:14,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:36:14,329 - openai._base_client - INFO - Retrying request to /embeddings in 0.828940 seconds
2025-06-30 22:36:17,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:36:17,115 - rag_service - ERROR - 函数 build_index 执行失败，耗时: 27.04秒，错误: 向量数据库错误: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-30 22:36:17,118 - main - ERROR - RAG服务初始化失败: 向量数据库错误: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-30 22:39:07,778 - main - INFO - 验证配置...
2025-06-30 22:39:07,779 - main - INFO - 配置验证通过
2025-06-30 22:39:07,779 - main - INFO - 启动性能监控...
2025-06-30 22:39:07,780 - monitoring - INFO - 性能监控已启动
2025-06-30 22:39:07,781 - main - INFO - 初始化RAG服务...
2025-06-30 22:39:16,911 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:39:16,912 - main - INFO - RAG服务初始化完成
2025-06-30 22:45:11,584 - httpx - INFO - HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"
2025-06-30 22:45:12,613 - httpx - INFO - HTTP Request: GET http://testserver/health "HTTP/1.1 200 OK"
2025-06-30 22:45:12,633 - httpx - INFO - HTTP Request: GET http://testserver/health "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:12,652 - request - INFO - 请求开始: POST /chat
2025-06-30 22:45:12,675 - request - INFO - 请求结束: 状态码=200, 耗时=0.00秒
2025-06-30 22:45:12,683 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 200 OK"
2025-06-30 22:45:12,697 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 422 Unprocessable Entity"
2025-06-30 22:45:13,383 - request - INFO - 请求开始: POST /chat
2025-06-30 22:45:13,387 - main - ERROR - 索引未构建: 向量索引未构建，请先调用build_index方法
2025-06-30 22:45:13,390 - request - ERROR - 请求错误: 向量索引未构建，请先调用build_index方法
Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 174, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1134, in __call__
    return self._mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1138, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1193, in _execute_mock_call
    raise effect
exceptions.IndexNotBuiltError: 向量索引未构建，请先调用build_index方法
2025-06-30 22:45:13,397 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:13,405 - request - INFO - 请求开始: POST /chat
2025-06-30 22:45:13,408 - main - ERROR - RAG系统错误: RAG系统错误
2025-06-30 22:45:13,410 - request - ERROR - 请求错误: RAG系统错误
Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 174, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1134, in __call__
    return self._mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1138, in _mock_call
    return self._execute_mock_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\unittest\mock.py", line 1193, in _execute_mock_call
    raise effect
exceptions.RAGException: RAG系统错误
2025-06-30 22:45:13,415 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 500 Internal Server Error"
2025-06-30 22:45:13,427 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:13,441 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:13,448 - request - INFO - 请求开始: POST /chat/stream
2025-06-30 22:45:13,452 - request - INFO - 请求结束: 状态码=200, 耗时=0.00秒
2025-06-30 22:45:13,455 - httpx - INFO - HTTP Request: POST http://testserver/chat/stream "HTTP/1.1 200 OK"
2025-06-30 22:45:13,463 - httpx - INFO - HTTP Request: POST http://testserver/chat/stream "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:13,474 - main - INFO - 索引构建完成
2025-06-30 22:45:13,476 - httpx - INFO - HTTP Request: POST http://testserver/index/build "HTTP/1.1 200 OK"
2025-06-30 22:45:13,486 - main - INFO - 索引构建完成
2025-06-30 22:45:13,489 - httpx - INFO - HTTP Request: POST http://testserver/index/build "HTTP/1.1 200 OK"
2025-06-30 22:45:13,497 - httpx - INFO - HTTP Request: GET http://testserver/index/status "HTTP/1.1 200 OK"
2025-06-30 22:45:13,505 - httpx - INFO - HTTP Request: GET http://testserver/index/status "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:45:13,514 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 422 Unprocessable Entity"
2025-06-30 22:45:13,518 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 422 Unprocessable Entity"
2025-06-30 22:45:13,523 - httpx - INFO - HTTP Request: POST http://testserver/chat "HTTP/1.1 422 Unprocessable Entity"
2025-06-30 22:45:13,533 - httpx - INFO - HTTP Request: POST http://testserver/index/build "HTTP/1.1 503 Service Unavailable"
2025-06-30 22:47:05,175 - main - INFO - 清理RAG服务资源...
2025-06-30 22:47:10,187 - monitoring - INFO - 性能监控已停止
2025-06-30 22:47:13,926 - main - INFO - 验证配置...
2025-06-30 22:47:13,926 - main - INFO - 配置验证通过
2025-06-30 22:47:13,927 - main - INFO - 启动性能监控...
2025-06-30 22:47:13,927 - monitoring - INFO - 性能监控已启动
2025-06-30 22:47:13,929 - main - INFO - 初始化RAG服务...
2025-06-30 22:47:19,092 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:47:19,093 - main - INFO - RAG服务初始化完成
2025-06-30 22:47:22,923 - main - INFO - 验证配置...
2025-06-30 22:47:22,924 - main - INFO - 配置验证通过
2025-06-30 22:47:22,925 - main - INFO - 启动性能监控...
2025-06-30 22:47:22,925 - monitoring - INFO - 性能监控已启动
2025-06-30 22:47:22,926 - main - INFO - 初始化RAG服务...
2025-06-30 22:47:28,108 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:47:28,109 - main - INFO - RAG服务初始化完成
2025-06-30 22:47:31,843 - main - INFO - 验证配置...
2025-06-30 22:47:31,844 - main - INFO - 配置验证通过
2025-06-30 22:47:31,845 - main - INFO - 启动性能监控...
2025-06-30 22:47:31,846 - monitoring - INFO - 性能监控已启动
2025-06-30 22:47:31,847 - main - INFO - 初始化RAG服务...
2025-06-30 22:47:36,530 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:47:36,531 - main - INFO - RAG服务初始化完成
2025-06-30 22:47:54,659 - main - INFO - 清理RAG服务资源...
2025-06-30 22:47:59,668 - monitoring - INFO - 性能监控已停止
2025-06-30 22:48:03,421 - main - INFO - 验证配置...
2025-06-30 22:48:03,422 - main - INFO - 配置验证通过
2025-06-30 22:48:03,423 - main - INFO - 启动性能监控...
2025-06-30 22:48:03,425 - monitoring - INFO - 性能监控已启动
2025-06-30 22:48:03,425 - main - INFO - 初始化RAG服务...
2025-06-30 22:48:07,370 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:48:07,373 - main - INFO - RAG服务初始化完成
2025-06-30 22:51:05,444 - main - INFO - 清理RAG服务资源...
2025-06-30 22:51:09,507 - monitoring - INFO - 性能监控已停止
2025-06-30 22:51:13,341 - main - INFO - 验证配置...
2025-06-30 22:51:13,342 - main - INFO - 配置验证通过
2025-06-30 22:51:13,343 - main - INFO - 启动性能监控...
2025-06-30 22:51:13,345 - monitoring - INFO - 性能监控已启动
2025-06-30 22:51:13,346 - main - INFO - 初始化RAG服务...
2025-06-30 22:51:17,750 - rag_service - INFO - RAG服务初始化完成
2025-06-30 22:51:17,751 - main - INFO - RAG服务初始化完成
2025-06-30 22:52:27,157 - request - INFO - 请求开始: POST /chat
2025-06-30 22:52:27,161 - RAGService - INFO - 开始处理查询: 介绍一下昨天的新闻...
2025-06-30 22:52:27,180 - rag_service - INFO - ---ROUTE QUESTION---
2025-06-30 22:52:29,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:52:29,024 - openai._base_client - INFO - Retrying request to /chat/completions in 0.450071 seconds
2025-06-30 22:52:29,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:52:29,887 - openai._base_client - INFO - Retrying request to /chat/completions in 0.888177 seconds
2025-06-30 22:52:31,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-06-30 22:52:31,548 - RAGService - ERROR - 查询处理时发生未预期错误: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 480, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 369, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 372, in invoke
    self.generate_prompt(
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 957, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 776, in generate
    self._generate_with_cache(
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1022, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1103, in _generate
    response = self.root_client.beta.chat.completions.parse(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 180, in parse
    return self._post(
           ^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
During task with name '__start__' and id '036a5f37-7169-fc8c-74b3-3133895b6531'
2025-06-30 22:52:31,582 - rag_service - ERROR - 函数 query 执行失败，耗时: 4.42秒，错误: 查询处理失败: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-30 22:52:31,584 - main - ERROR - RAG系统错误: 查询处理失败: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-30 22:52:31,586 - request - ERROR - 请求错误: 查询处理失败: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 480, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 369, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 5431, in invoke
    return self.bound.invoke(
           ^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 372, in invoke
    self.generate_prompt(
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 957, in generate_prompt
    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 776, in generate
    self._generate_with_cache(
  File "D:\Python\Lib\site-packages\langchain_core\language_models\chat_models.py", line 1022, in _generate_with_cache
    result = self._generate(
             ^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_openai\chat_models\base.py", line 1103, in _generate
    response = self.root_client.beta.chat.completions.parse(**payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\resources\chat\completions\completions.py", line 180, in parse
    return self._post(
           ^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\openai\_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
During task with name '__start__' and id '036a5f37-7169-fc8c-74b3-3133895b6531'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 498, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-06-30 23:03:34,381 - main - INFO - 清理RAG服务资源...
2025-06-30 23:03:37,766 - monitoring - INFO - 性能监控已停止
2025-06-30 23:03:41,474 - main - INFO - 验证配置...
2025-06-30 23:03:41,474 - main - INFO - 配置验证通过
2025-06-30 23:03:41,475 - main - INFO - 启动性能监控...
2025-06-30 23:03:41,476 - monitoring - INFO - 性能监控已启动
2025-06-30 23:03:41,477 - main - INFO - 初始化RAG服务...
2025-06-30 23:03:41,478 - main - ERROR - RAG服务初始化失败: 'RAGConfig' object has no attribute 'use_local_model'
2025-06-30 23:04:00,633 - main - INFO - 验证配置...
2025-06-30 23:04:00,634 - main - INFO - 配置验证通过
2025-06-30 23:04:00,634 - main - INFO - 启动性能监控...
2025-06-30 23:04:00,636 - monitoring - INFO - 性能监控已启动
2025-06-30 23:04:00,636 - main - INFO - 初始化RAG服务...
2025-06-30 23:04:00,637 - main - ERROR - RAG服务初始化失败: 'RAGConfig' object has no attribute 'use_local_model'
2025-06-30 23:04:07,816 - main - INFO - 验证配置...
2025-06-30 23:04:07,817 - main - INFO - 配置验证通过
2025-06-30 23:04:07,818 - main - INFO - 启动性能监控...
2025-06-30 23:04:07,820 - monitoring - INFO - 性能监控已启动
2025-06-30 23:04:07,820 - main - INFO - 初始化RAG服务...
2025-06-30 23:04:12,304 - rag_service - INFO - RAG服务初始化完成
2025-06-30 23:04:12,305 - main - INFO - RAG服务初始化完成
2025-06-30 23:04:13,938 - main - INFO - 清理RAG服务资源...
2025-06-30 23:04:18,948 - monitoring - INFO - 性能监控已停止
2025-06-30 23:04:22,974 - main - INFO - 验证配置...
2025-06-30 23:04:22,975 - main - INFO - 配置验证通过
2025-06-30 23:04:22,976 - main - INFO - 启动性能监控...
2025-06-30 23:04:22,977 - monitoring - INFO - 性能监控已启动
2025-06-30 23:04:22,977 - main - INFO - 初始化RAG服务...
2025-06-30 23:04:22,978 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-06-30 23:04:23,233 - main - ERROR - RAG服务初始化失败: 
2025-06-30 23:04:44,731 - main - INFO - 验证配置...
2025-06-30 23:04:44,732 - main - INFO - 配置验证通过
2025-06-30 23:04:44,734 - main - INFO - 启动性能监控...
2025-06-30 23:04:44,735 - monitoring - INFO - 性能监控已启动
2025-06-30 23:04:44,735 - main - INFO - 初始化RAG服务...
2025-06-30 23:04:44,736 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-06-30 23:04:44,977 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:48:55,437 - main - INFO - 验证配置...
2025-07-01 08:48:55,437 - main - INFO - 配置验证通过
2025-07-01 08:48:55,438 - main - INFO - 启动性能监控...
2025-07-01 08:48:55,438 - monitoring - INFO - 性能监控已启动
2025-07-01 08:48:55,439 - main - INFO - 初始化RAG服务...
2025-07-01 08:48:55,439 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:48:55,641 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:49:23,379 - main - INFO - 验证配置...
2025-07-01 08:49:23,380 - main - INFO - 配置验证通过
2025-07-01 08:49:23,380 - main - INFO - 启动性能监控...
2025-07-01 08:49:23,383 - monitoring - INFO - 性能监控已启动
2025-07-01 08:49:23,384 - main - INFO - 初始化RAG服务...
2025-07-01 08:49:23,384 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:49:23,580 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:49:36,493 - main - INFO - 验证配置...
2025-07-01 08:49:36,493 - main - INFO - 配置验证通过
2025-07-01 08:49:36,493 - main - INFO - 启动性能监控...
2025-07-01 08:49:36,494 - monitoring - INFO - 性能监控已启动
2025-07-01 08:49:36,495 - main - INFO - 初始化RAG服务...
2025-07-01 08:49:36,495 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:49:36,681 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:51:47,281 - main - INFO - 验证配置...
2025-07-01 08:51:47,281 - main - INFO - 配置验证通过
2025-07-01 08:51:47,282 - main - INFO - 启动性能监控...
2025-07-01 08:51:47,283 - monitoring - INFO - 性能监控已启动
2025-07-01 08:51:47,283 - main - INFO - 初始化RAG服务...
2025-07-01 08:51:47,283 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:51:47,486 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:51:56,551 - main - INFO - 验证配置...
2025-07-01 08:51:56,551 - main - INFO - 配置验证通过
2025-07-01 08:51:56,552 - main - INFO - 启动性能监控...
2025-07-01 08:51:56,552 - monitoring - INFO - 性能监控已启动
2025-07-01 08:51:56,553 - main - INFO - 初始化RAG服务...
2025-07-01 08:51:56,553 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:51:56,748 - main - ERROR - RAG服务初始化失败: 
2025-07-01 08:52:07,063 - main - INFO - 验证配置...
2025-07-01 08:52:07,064 - main - INFO - 配置验证通过
2025-07-01 08:52:07,064 - main - INFO - 启动性能监控...
2025-07-01 08:52:07,065 - monitoring - INFO - 性能监控已启动
2025-07-01 08:52:07,066 - main - INFO - 初始化RAG服务...
2025-07-01 08:52:07,066 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:52:08,481 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:52:08,482 - main - INFO - RAG服务初始化完成
2025-07-01 08:52:15,779 - main - INFO - 清理RAG服务资源...
2025-07-01 08:52:20,788 - monitoring - INFO - 性能监控已停止
2025-07-01 08:52:23,481 - main - INFO - 验证配置...
2025-07-01 08:52:23,481 - main - INFO - 配置验证通过
2025-07-01 08:52:23,481 - main - INFO - 启动性能监控...
2025-07-01 08:52:23,482 - monitoring - INFO - 性能监控已启动
2025-07-01 08:52:23,483 - main - INFO - 初始化RAG服务...
2025-07-01 08:52:23,483 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:52:24,410 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:52:24,410 - main - INFO - RAG服务初始化完成
2025-07-01 08:52:45,249 - request - INFO - 请求开始: POST /chat
2025-07-01 08:52:45,249 - RAGService - INFO - 开始处理查询: 你是什么模型...
2025-07-01 08:52:45,253 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 08:52:45,260 - RAGService - ERROR - 查询处理时发生未预期错误: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 610, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 488, in route_question
    source = self.question_router(question)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 222, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 134, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '25ce7f83-06f7-4c27-f0ba-8826eb808bc0'
2025-07-01 08:52:45,267 - rag_service - ERROR - 函数 query 执行失败，耗时: 0.02秒，错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:52:45,267 - main - ERROR - RAG系统错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:52:45,268 - request - ERROR - 请求错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 610, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 488, in route_question
    source = self.question_router(question)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 222, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 134, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '25ce7f83-06f7-4c27-f0ba-8826eb808bc0'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 628, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:54:23,748 - main - INFO - 清理RAG服务资源...
2025-07-01 08:54:27,530 - monitoring - INFO - 性能监控已停止
2025-07-01 08:54:30,318 - main - INFO - 验证配置...
2025-07-01 08:54:30,319 - main - INFO - 配置验证通过
2025-07-01 08:54:30,319 - main - INFO - 启动性能监控...
2025-07-01 08:54:30,320 - monitoring - INFO - 性能监控已启动
2025-07-01 08:54:30,320 - main - INFO - 初始化RAG服务...
2025-07-01 08:54:30,321 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:54:31,058 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:54:31,059 - main - INFO - RAG服务初始化完成
2025-07-01 08:54:31,271 - main - INFO - 清理RAG服务资源...
2025-07-01 08:54:36,272 - monitoring - INFO - 性能监控已停止
2025-07-01 08:54:39,161 - main - INFO - 验证配置...
2025-07-01 08:54:39,161 - main - INFO - 配置验证通过
2025-07-01 08:54:39,162 - main - INFO - 启动性能监控...
2025-07-01 08:54:39,162 - monitoring - INFO - 性能监控已启动
2025-07-01 08:54:39,163 - main - INFO - 初始化RAG服务...
2025-07-01 08:54:39,163 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:54:39,914 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:54:39,915 - main - INFO - RAG服务初始化完成
2025-07-01 08:54:42,743 - main - INFO - 验证配置...
2025-07-01 08:54:42,744 - main - INFO - 配置验证通过
2025-07-01 08:54:42,744 - main - INFO - 启动性能监控...
2025-07-01 08:54:42,745 - monitoring - INFO - 性能监控已启动
2025-07-01 08:54:42,745 - main - INFO - 初始化RAG服务...
2025-07-01 08:54:42,746 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:54:43,523 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:54:43,523 - main - INFO - RAG服务初始化完成
2025-07-01 08:54:57,375 - request - INFO - 请求开始: POST /chat
2025-07-01 08:54:57,376 - RAGService - INFO - 开始处理查询: 你是什么模型...
2025-07-01 08:54:57,380 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 08:54:57,387 - RAGService - ERROR - 查询处理时发生未预期错误: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 610, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 488, in route_question
    source = self.question_router(question)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 222, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 137, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '011ef413-8d0e-fcd6-9fb3-1a9f4977bb6e'
2025-07-01 08:54:57,392 - rag_service - ERROR - 函数 query 执行失败，耗时: 0.02秒，错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:54:57,393 - main - ERROR - RAG系统错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:54:57,394 - request - ERROR - 请求错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 610, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 488, in route_question
    source = self.question_router(question)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 222, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 137, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '011ef413-8d0e-fcd6-9fb3-1a9f4977bb6e'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 628, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 08:57:00,739 - main - INFO - 清理RAG服务资源...
2025-07-01 08:57:05,748 - monitoring - INFO - 性能监控已停止
2025-07-01 08:57:08,703 - main - INFO - 验证配置...
2025-07-01 08:57:08,704 - main - INFO - 配置验证通过
2025-07-01 08:57:08,704 - main - INFO - 启动性能监控...
2025-07-01 08:57:08,705 - monitoring - INFO - 性能监控已启动
2025-07-01 08:57:08,705 - main - INFO - 初始化RAG服务...
2025-07-01 08:57:08,706 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:57:09,583 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:57:09,583 - main - INFO - RAG服务初始化完成
2025-07-01 08:57:26,912 - main - INFO - 清理RAG服务资源...
2025-07-01 08:57:31,925 - monitoring - INFO - 性能监控已停止
2025-07-01 08:57:34,754 - main - INFO - 验证配置...
2025-07-01 08:57:34,755 - main - INFO - 配置验证通过
2025-07-01 08:57:34,755 - main - INFO - 启动性能监控...
2025-07-01 08:57:34,756 - monitoring - INFO - 性能监控已启动
2025-07-01 08:57:34,757 - main - INFO - 初始化RAG服务...
2025-07-01 08:57:34,757 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:57:35,716 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:57:35,717 - main - INFO - RAG服务初始化完成
2025-07-01 08:57:49,557 - main - INFO - 清理RAG服务资源...
2025-07-01 08:57:54,571 - monitoring - INFO - 性能监控已停止
2025-07-01 08:57:57,371 - main - INFO - 验证配置...
2025-07-01 08:57:57,372 - main - INFO - 配置验证通过
2025-07-01 08:57:57,372 - main - INFO - 启动性能监控...
2025-07-01 08:57:57,373 - monitoring - INFO - 性能监控已启动
2025-07-01 08:57:57,373 - main - INFO - 初始化RAG服务...
2025-07-01 08:57:57,373 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:57:58,096 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:57:58,097 - main - INFO - RAG服务初始化完成
2025-07-01 08:58:11,507 - main - INFO - 清理RAG服务资源...
2025-07-01 08:58:16,518 - monitoring - INFO - 性能监控已停止
2025-07-01 08:58:19,366 - main - INFO - 验证配置...
2025-07-01 08:58:19,366 - main - INFO - 配置验证通过
2025-07-01 08:58:19,367 - main - INFO - 启动性能监控...
2025-07-01 08:58:19,368 - monitoring - INFO - 性能监控已启动
2025-07-01 08:58:19,368 - main - INFO - 初始化RAG服务...
2025-07-01 08:58:19,368 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:58:20,297 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:58:20,297 - main - INFO - RAG服务初始化完成
2025-07-01 08:58:28,696 - main - INFO - 清理RAG服务资源...
2025-07-01 08:58:33,698 - monitoring - INFO - 性能监控已停止
2025-07-01 08:58:36,496 - main - INFO - 验证配置...
2025-07-01 08:58:36,496 - main - INFO - 配置验证通过
2025-07-01 08:58:36,496 - main - INFO - 启动性能监控...
2025-07-01 08:58:36,497 - monitoring - INFO - 性能监控已启动
2025-07-01 08:58:36,497 - main - INFO - 初始化RAG服务...
2025-07-01 08:58:36,498 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:58:37,380 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:58:37,381 - main - INFO - RAG服务初始化完成
2025-07-01 08:58:42,988 - main - INFO - 清理RAG服务资源...
2025-07-01 08:58:48,064 - monitoring - INFO - 性能监控已停止
2025-07-01 08:58:50,882 - main - INFO - 验证配置...
2025-07-01 08:58:50,882 - main - INFO - 配置验证通过
2025-07-01 08:58:50,883 - main - INFO - 启动性能监控...
2025-07-01 08:58:50,884 - monitoring - INFO - 性能监控已启动
2025-07-01 08:58:50,884 - main - INFO - 初始化RAG服务...
2025-07-01 08:58:50,885 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:58:51,641 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:58:51,642 - main - INFO - RAG服务初始化完成
2025-07-01 08:58:58,413 - main - INFO - 清理RAG服务资源...
2025-07-01 08:59:03,413 - monitoring - INFO - 性能监控已停止
2025-07-01 08:59:06,260 - main - INFO - 验证配置...
2025-07-01 08:59:06,260 - main - INFO - 配置验证通过
2025-07-01 08:59:06,261 - main - INFO - 启动性能监控...
2025-07-01 08:59:06,262 - monitoring - INFO - 性能监控已启动
2025-07-01 08:59:06,262 - main - INFO - 初始化RAG服务...
2025-07-01 08:59:06,262 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:59:07,148 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:59:07,149 - main - INFO - RAG服务初始化完成
2025-07-01 08:59:31,679 - main - INFO - 清理RAG服务资源...
2025-07-01 08:59:36,685 - monitoring - INFO - 性能监控已停止
2025-07-01 08:59:39,630 - main - INFO - 验证配置...
2025-07-01 08:59:39,630 - main - INFO - 配置验证通过
2025-07-01 08:59:39,631 - main - INFO - 启动性能监控...
2025-07-01 08:59:39,631 - monitoring - INFO - 性能监控已启动
2025-07-01 08:59:39,632 - main - INFO - 初始化RAG服务...
2025-07-01 08:59:39,632 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 08:59:40,352 - rag_service - INFO - RAG服务初始化完成
2025-07-01 08:59:40,352 - main - INFO - RAG服务初始化完成
2025-07-01 08:59:59,894 - main - INFO - 清理RAG服务资源...
2025-07-01 09:00:04,910 - monitoring - INFO - 性能监控已停止
2025-07-01 09:00:08,353 - main - INFO - 验证配置...
2025-07-01 09:00:08,354 - main - INFO - 配置验证通过
2025-07-01 09:00:08,354 - main - INFO - 启动性能监控...
2025-07-01 09:00:08,355 - monitoring - INFO - 性能监控已启动
2025-07-01 09:00:08,355 - main - INFO - 初始化RAG服务...
2025-07-01 09:00:08,356 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:00:20,278 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:00:20,280 - main - INFO - RAG服务初始化完成
2025-07-01 09:01:56,487 - request - INFO - 请求开始: POST /chat
2025-07-01 09:01:56,488 - RAGService - INFO - 开始处理查询: 你是什么模型...
2025-07-01 09:01:56,490 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 09:01:56,495 - RAGService - ERROR - 查询处理时发生未预期错误: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 635, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 513, in route_question
    source = self.question_router({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 223, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 137, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '4395661c-e4e9-8d68-662e-5575a20667a6'
2025-07-01 09:01:56,500 - rag_service - ERROR - 函数 query 执行失败，耗时: 0.01秒，错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 09:01:56,501 - main - ERROR - RAG系统错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 09:01:56,501 - request - ERROR - 请求错误: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 635, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 513, in route_question
    source = self.question_router({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 223, in route_wrapper
    return router_func(question)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 137, in structured_output_wrapper
    result = chain.invoke({"input": input_data})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3045, in invoke
    input_ = context.run(step.invoke, input_, config, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 216, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 189, in _format_prompt_with_error_handling
    _inner_input = self._validate_input(inner_input)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\prompts\base.py", line 183, in _validate_input
    raise KeyError(
KeyError: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
During task with name '__start__' and id '4395661c-e4e9-8d68-662e-5575a20667a6'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 653, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: 'Input to ChatPromptTemplate is missing variables {\'"description"\'}.  Expected: [\'"description"\', \'input\'] Received: [\'input\']\nNote: if you intended {"description"} to be part of the string and not a variable, please escape it with double curly braces like: \'{{"description"}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT '
2025-07-01 09:03:45,730 - main - INFO - 清理RAG服务资源...
2025-07-01 09:03:50,742 - monitoring - INFO - 性能监控已停止
2025-07-01 09:03:53,877 - main - INFO - 验证配置...
2025-07-01 09:03:53,877 - main - INFO - 配置验证通过
2025-07-01 09:03:53,878 - main - INFO - 启动性能监控...
2025-07-01 09:03:53,879 - monitoring - INFO - 性能监控已启动
2025-07-01 09:03:53,880 - main - INFO - 初始化RAG服务...
2025-07-01 09:03:53,880 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:03:54,661 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:03:54,662 - main - INFO - RAG服务初始化完成
2025-07-01 09:04:01,493 - main - INFO - 清理RAG服务资源...
2025-07-01 09:04:06,559 - monitoring - INFO - 性能监控已停止
2025-07-01 09:04:09,369 - main - INFO - 验证配置...
2025-07-01 09:04:09,369 - main - INFO - 配置验证通过
2025-07-01 09:04:09,370 - main - INFO - 启动性能监控...
2025-07-01 09:04:09,371 - monitoring - INFO - 性能监控已启动
2025-07-01 09:04:09,371 - main - INFO - 初始化RAG服务...
2025-07-01 09:04:09,372 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:04:10,068 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:04:10,068 - main - INFO - RAG服务初始化完成
2025-07-01 09:04:21,848 - main - INFO - 清理RAG服务资源...
2025-07-01 09:04:26,861 - monitoring - INFO - 性能监控已停止
2025-07-01 09:04:29,710 - main - INFO - 验证配置...
2025-07-01 09:04:29,711 - main - INFO - 配置验证通过
2025-07-01 09:04:29,711 - main - INFO - 启动性能监控...
2025-07-01 09:04:29,712 - monitoring - INFO - 性能监控已启动
2025-07-01 09:04:29,712 - main - INFO - 初始化RAG服务...
2025-07-01 09:04:29,713 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:04:30,318 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:04:30,319 - main - INFO - RAG服务初始化完成
2025-07-01 09:04:40,342 - main - INFO - 清理RAG服务资源...
2025-07-01 09:04:45,354 - monitoring - INFO - 性能监控已停止
2025-07-01 09:04:48,145 - main - INFO - 验证配置...
2025-07-01 09:04:48,146 - main - INFO - 配置验证通过
2025-07-01 09:04:48,146 - main - INFO - 启动性能监控...
2025-07-01 09:04:48,147 - monitoring - INFO - 性能监控已启动
2025-07-01 09:04:48,147 - main - INFO - 初始化RAG服务...
2025-07-01 09:04:48,148 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:04:48,810 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:04:48,811 - main - INFO - RAG服务初始化完成
2025-07-01 09:05:12,470 - main - INFO - 清理RAG服务资源...
2025-07-01 09:05:17,481 - monitoring - INFO - 性能监控已停止
2025-07-01 09:05:20,488 - main - INFO - 验证配置...
2025-07-01 09:05:20,489 - main - INFO - 配置验证通过
2025-07-01 09:05:20,489 - main - INFO - 启动性能监控...
2025-07-01 09:05:20,490 - monitoring - INFO - 性能监控已启动
2025-07-01 09:05:20,490 - main - INFO - 初始化RAG服务...
2025-07-01 09:05:20,491 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:05:21,143 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:05:21,143 - main - INFO - RAG服务初始化完成
2025-07-01 09:06:01,905 - main - INFO - 清理RAG服务资源...
2025-07-01 09:06:06,909 - monitoring - INFO - 性能监控已停止
2025-07-01 09:06:09,821 - main - INFO - 验证配置...
2025-07-01 09:06:09,821 - main - INFO - 配置验证通过
2025-07-01 09:06:09,822 - main - INFO - 启动性能监控...
2025-07-01 09:06:09,823 - monitoring - INFO - 性能监控已启动
2025-07-01 09:06:09,823 - main - INFO - 初始化RAG服务...
2025-07-01 09:06:09,823 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:06:10,050 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:06:29,370 - main - INFO - 验证配置...
2025-07-01 09:06:29,370 - main - INFO - 配置验证通过
2025-07-01 09:06:29,371 - main - INFO - 启动性能监控...
2025-07-01 09:06:29,371 - monitoring - INFO - 性能监控已启动
2025-07-01 09:06:29,372 - main - INFO - 初始化RAG服务...
2025-07-01 09:06:29,372 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:06:29,663 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:06:51,030 - main - INFO - 验证配置...
2025-07-01 09:06:51,031 - main - INFO - 配置验证通过
2025-07-01 09:06:51,031 - main - INFO - 启动性能监控...
2025-07-01 09:06:51,031 - monitoring - INFO - 性能监控已启动
2025-07-01 09:06:51,033 - main - INFO - 初始化RAG服务...
2025-07-01 09:06:51,033 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:06:51,227 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:07:16,005 - main - INFO - 验证配置...
2025-07-01 09:07:16,005 - main - INFO - 配置验证通过
2025-07-01 09:07:16,006 - main - INFO - 启动性能监控...
2025-07-01 09:07:16,007 - monitoring - INFO - 性能监控已启动
2025-07-01 09:07:16,007 - main - INFO - 初始化RAG服务...
2025-07-01 09:07:16,008 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:07:16,203 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:07:33,681 - main - INFO - 验证配置...
2025-07-01 09:07:33,681 - main - INFO - 配置验证通过
2025-07-01 09:07:33,682 - main - INFO - 启动性能监控...
2025-07-01 09:07:33,682 - monitoring - INFO - 性能监控已启动
2025-07-01 09:07:33,683 - main - INFO - 初始化RAG服务...
2025-07-01 09:07:33,683 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:07:33,893 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:07:47,796 - main - INFO - 验证配置...
2025-07-01 09:07:47,797 - main - INFO - 配置验证通过
2025-07-01 09:07:47,797 - main - INFO - 启动性能监控...
2025-07-01 09:07:47,798 - monitoring - INFO - 性能监控已启动
2025-07-01 09:07:47,798 - main - INFO - 初始化RAG服务...
2025-07-01 09:07:47,799 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:07:47,996 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:08:03,607 - main - INFO - 验证配置...
2025-07-01 09:08:03,608 - main - INFO - 配置验证通过
2025-07-01 09:08:03,609 - main - INFO - 启动性能监控...
2025-07-01 09:08:03,609 - monitoring - INFO - 性能监控已启动
2025-07-01 09:08:03,610 - main - INFO - 初始化RAG服务...
2025-07-01 09:08:03,610 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:08:03,805 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:08:17,501 - main - INFO - 验证配置...
2025-07-01 09:08:17,502 - main - INFO - 配置验证通过
2025-07-01 09:08:17,502 - main - INFO - 启动性能监控...
2025-07-01 09:08:17,503 - monitoring - INFO - 性能监控已启动
2025-07-01 09:08:17,503 - main - INFO - 初始化RAG服务...
2025-07-01 09:08:17,504 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:08:17,708 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:08:49,224 - main - INFO - 验证配置...
2025-07-01 09:08:49,224 - main - INFO - 配置验证通过
2025-07-01 09:08:49,225 - main - INFO - 启动性能监控...
2025-07-01 09:08:49,226 - monitoring - INFO - 性能监控已启动
2025-07-01 09:08:49,226 - main - INFO - 初始化RAG服务...
2025-07-01 09:08:49,226 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:08:49,417 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:12:29,258 - main - INFO - 验证配置...
2025-07-01 09:12:29,258 - main - INFO - 配置验证通过
2025-07-01 09:12:29,258 - main - INFO - 启动性能监控...
2025-07-01 09:12:29,259 - monitoring - INFO - 性能监控已启动
2025-07-01 09:12:29,259 - main - INFO - 初始化RAG服务...
2025-07-01 09:12:29,260 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:12:29,452 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:12:49,588 - main - INFO - 验证配置...
2025-07-01 09:12:49,589 - main - INFO - 配置验证通过
2025-07-01 09:12:49,589 - main - INFO - 启动性能监控...
2025-07-01 09:12:49,590 - monitoring - INFO - 性能监控已启动
2025-07-01 09:12:49,591 - main - INFO - 初始化RAG服务...
2025-07-01 09:12:49,591 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:12:49,782 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:13:12,822 - main - INFO - 验证配置...
2025-07-01 09:13:12,823 - main - INFO - 配置验证通过
2025-07-01 09:13:12,823 - main - INFO - 启动性能监控...
2025-07-01 09:13:12,824 - monitoring - INFO - 性能监控已启动
2025-07-01 09:13:12,824 - main - INFO - 初始化RAG服务...
2025-07-01 09:13:12,825 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:13:13,009 - main - ERROR - RAG服务初始化失败: 
2025-07-01 09:13:30,817 - main - INFO - 验证配置...
2025-07-01 09:13:30,817 - main - INFO - 配置验证通过
2025-07-01 09:13:30,818 - main - INFO - 启动性能监控...
2025-07-01 09:13:30,818 - monitoring - INFO - 性能监控已启动
2025-07-01 09:13:30,818 - main - INFO - 初始化RAG服务...
2025-07-01 09:13:30,819 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:13:31,409 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:13:31,409 - main - INFO - RAG服务初始化完成
2025-07-01 09:14:22,667 - main - INFO - 清理RAG服务资源...
2025-07-01 09:14:27,675 - monitoring - INFO - 性能监控已停止
2025-07-01 09:14:30,993 - main - INFO - 验证配置...
2025-07-01 09:14:30,994 - main - INFO - 配置验证通过
2025-07-01 09:14:30,994 - main - INFO - 启动性能监控...
2025-07-01 09:14:30,995 - monitoring - INFO - 性能监控已启动
2025-07-01 09:14:30,995 - main - INFO - 初始化RAG服务...
2025-07-01 09:14:30,996 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:14:31,631 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:14:31,632 - main - INFO - RAG服务初始化完成
2025-07-01 09:15:01,918 - main - INFO - 清理RAG服务资源...
2025-07-01 09:15:02,003 - monitoring - INFO - 性能监控已停止
2025-07-01 09:15:08,659 - main - INFO - 验证配置...
2025-07-01 09:15:08,659 - main - INFO - 配置验证通过
2025-07-01 09:15:08,659 - main - INFO - 启动性能监控...
2025-07-01 09:15:08,660 - monitoring - INFO - 性能监控已启动
2025-07-01 09:15:08,660 - main - INFO - 初始化RAG服务...
2025-07-01 09:15:08,661 - RAGService - INFO - 使用本地Ollama模型: qwen:0.6b
2025-07-01 09:15:09,329 - rag_service - INFO - RAG服务初始化完成
2025-07-01 09:15:09,330 - main - INFO - RAG服务初始化完成
2025-07-01 10:37:02,235 - main - INFO - 清理RAG服务资源...
2025-07-01 10:37:07,243 - monitoring - INFO - 性能监控已停止
2025-07-01 15:04:18,778 - main - INFO - 验证配置...
2025-07-01 15:04:18,779 - main - INFO - 配置验证通过
2025-07-01 15:04:18,779 - main - INFO - 启动性能监控...
2025-07-01 15:04:18,780 - monitoring - INFO - 性能监控已启动
2025-07-01 15:04:18,780 - main - INFO - 初始化RAG服务...
2025-07-01 15:04:18,780 - RAGService - INFO - 使用本地Ollama模型: qwen3:0.6b
2025-07-01 15:04:19,196 - rag_service - INFO - RAG服务初始化完成
2025-07-01 15:04:19,196 - main - INFO - RAG服务初始化完成
2025-07-01 15:04:46,592 - request - INFO - 请求开始: POST /chat
2025-07-01 15:04:46,592 - RAGService - INFO - 开始处理查询: a...
2025-07-01 15:04:46,595 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 15:04:51,051 - RAGService - ERROR - 查询处理时发生未预期错误: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    text = output.content.strip().lower()
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id '0669b6fc-1203-f452-d0d8-f06038f5a7e4'
2025-07-01 15:04:51,060 - rag_service - ERROR - 函数 query 执行失败，耗时: 4.47秒，错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:04:51,061 - main - ERROR - RAG系统错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:04:51,061 - request - ERROR - 请求错误: 查询处理失败: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    text = output.content.strip().lower()
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id '0669b6fc-1203-f452-d0d8-f06038f5a7e4'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 631, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:05:37,584 - request - INFO - 请求开始: POST /chat
2025-07-01 15:05:37,585 - RAGService - INFO - 开始处理查询: 你是什么模型...
2025-07-01 15:05:37,586 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 15:05:42,441 - RAGService - ERROR - 查询处理时发生未预期错误: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    text = output.content.strip().lower()
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id 'fb98c6fa-cdee-eb8a-c7c9-5b50e7388223'
2025-07-01 15:05:42,445 - rag_service - ERROR - 函数 query 执行失败，耗时: 4.86秒，错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:05:42,446 - main - ERROR - RAG系统错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:05:42,446 - request - ERROR - 请求错误: 查询处理失败: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    source = self.question_router.invoke({"question": question})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    text = output.content.strip().lower()
           ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id 'fb98c6fa-cdee-eb8a-c7c9-5b50e7388223'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 631, in query
    raise RAGException(f"查询处理失败: {str(e)}")
exceptions.RAGException: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:11:15,471 - request - INFO - 请求开始: POST /chat
2025-07-01 15:11:15,472 - RAGService - INFO - 开始处理查询: 你是什么模型...
2025-07-01 15:11:15,473 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-01 15:11:21,615 - RAGService - ERROR - 查询处理时发生未预期错误: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    if not question or not question.strip():
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    self.logger.error(f"网络搜索失败: {str(e)}")
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    Respond with EXACTLY one word: either "vectorstore" or "websearch"."""
               ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id 'bfcd93a8-1c9e-58a6-348a-c9f1b3c02841'
2025-07-01 15:11:21,622 - rag_service - ERROR - 函数 query 执行失败，耗时: 6.15秒，错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:11:21,623 - main - ERROR - RAG系统错误: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:11:21,623 - request - ERROR - 请求错误: 查询处理失败: 'str' object has no attribute 'content'
Traceback (most recent call last):
  File "D:\workspace\forjob_py\rag_service.py", line 613, in query
    if not question or not question.strip():
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 625, in invoke
    input = step.invoke(input, config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\graph\branch.py", line 169, in _route
    result = self.path.invoke(value, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 370, in invoke
    ret = context.run(self.func, *args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 499, in route_question
    self.logger.error(f"网络搜索失败: {str(e)}")
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 3047, in invoke
    input_ = context.run(step.invoke, input_, config)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4772, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 1940, in _call_with_config
    context.run(
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\base.py", line 4630, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langchain_core\runnables\config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 241, in parse_route_output
    Respond with EXACTLY one word: either "vectorstore" or "websearch"."""
               ^^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'content'
During task with name '__start__' and id 'bfcd93a8-1c9e-58a6-348a-c9f1b3c02841'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\workspace\forjob_py\rag_service.py", line 631, in query
    else:
exceptions.RAGException: 查询处理失败: 'str' object has no attribute 'content'
2025-07-01 15:46:44,172 - main - INFO - 清理RAG服务资源...
2025-07-01 15:46:49,174 - monitoring - INFO - 性能监控已停止
2025-07-10 16:28:15,414 - main - INFO - 验证配置...
2025-07-10 16:28:15,415 - main - INFO - 配置验证通过
2025-07-10 16:28:15,415 - main - INFO - 启动性能监控...
2025-07-10 16:28:15,416 - monitoring - INFO - 性能监控已启动
2025-07-10 16:28:15,417 - main - INFO - 初始化RAG服务...
2025-07-10 16:28:15,417 - RAGService - INFO - 使用本地Ollama模型: qwen3:0.6b
2025-07-10 16:28:20,174 - rag_service - INFO - RAG服务初始化完成
2025-07-10 16:28:20,176 - main - INFO - RAG服务初始化完成
2025-07-10 16:33:07,065 - main - INFO - 清理RAG服务资源...
2025-07-10 16:33:12,066 - monitoring - INFO - 性能监控已停止
2025-07-10 16:33:15,204 - main - INFO - 验证配置...
2025-07-10 16:33:15,204 - main - INFO - 配置验证通过
2025-07-10 16:33:15,207 - main - INFO - 启动性能监控...
2025-07-10 16:33:15,208 - monitoring - INFO - 性能监控已启动
2025-07-10 16:33:15,208 - main - INFO - 初始化RAG服务...
2025-07-10 16:33:15,208 - RAGService - INFO - 使用本地Ollama模型: qwen3:0.6b
2025-07-10 16:33:15,861 - rag_service - INFO - RAG服务初始化完成
2025-07-10 16:33:15,861 - main - INFO - RAG服务初始化完成
2025-07-10 16:38:24,294 - request - INFO - 请求开始: POST /chat
2025-07-10 16:38:24,294 - RAGService - INFO - 开始处理查询: 测试一下...
2025-07-10 16:38:24,301 - rag_service - INFO - ---ROUTE QUESTION---
2025-07-10 16:38:31,160 - rag_service - INFO - ---ROUTE QUESTION TO RAG---
2025-07-10 16:38:31,162 - RAGService - INFO - ---RETRIEVE---
2025-07-10 16:38:31,162 - rag_service - ERROR - 函数 query 执行失败，耗时: 6.87秒，错误: 向量索引未构建，请先调用build_index方法
2025-07-10 16:38:31,163 - main - ERROR - 索引未构建: 向量索引未构建，请先调用build_index方法
2025-07-10 16:38:31,164 - request - ERROR - 请求错误: 向量索引未构建，请先调用build_index方法
Traceback (most recent call last):
  File "d:\workspace\forjob_py\main.py", line 186, in chat
    response = rag_service.query(request.message)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\workspace\forjob_py\logger.py", line 175, in wrapper
    result = func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^
  File "d:\workspace\forjob_py\rag_service.py", line 622, in query
    for output in self.app.stream(inputs):
  File "D:\Python\Lib\site-packages\langgraph\pregel\__init__.py", line 2542, in stream
    for _ in runner.tick(
  File "D:\Python\Lib\site-packages\langgraph\pregel\runner.py", line 162, in tick
    run_with_retry(
  File "D:\Python\Lib\site-packages\langgraph\pregel\retry.py", line 42, in run_with_retry
    return task.proc.invoke(task.input, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 623, in invoke
    input = context.run(step.invoke, input, config, **kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Python\Lib\site-packages\langgraph\utils\runnable.py", line 377, in invoke
    ret = self.func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\workspace\forjob_py\rag_service.py", line 421, in retrieve
    raise IndexNotBuiltError()
exceptions.IndexNotBuiltError: 向量索引未构建，请先调用build_index方法
During task with name 'retrieve' and id 'b3d62c51-2dbb-9c6a-fb82-ec914683059e'
2025-07-10 16:45:24,446 - main - INFO - 清理RAG服务资源...
2025-07-10 16:45:29,453 - monitoring - INFO - 性能监控已停止
2025-07-10 16:45:33,084 - main - INFO - 验证配置...
2025-07-10 16:45:33,085 - main - INFO - 配置验证通过
2025-07-10 16:45:33,085 - main - INFO - 启动性能监控...
2025-07-10 16:45:33,087 - monitoring - INFO - 性能监控已启动
2025-07-10 16:45:33,087 - main - INFO - 初始化RAG服务...
2025-07-10 16:45:33,088 - RAGService - INFO - 使用本地Ollama模型: qwen3:0.6b
2025-07-10 16:45:33,739 - rag_service - INFO - RAG服务初始化完成
2025-07-10 16:45:33,739 - main - INFO - RAG服务初始化完成
