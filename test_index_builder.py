#!/usr/bin/env python3
"""
测试索引构建器页面功能
"""

import requests
import json
import time

def test_index_build_api():
    """测试索引构建API"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试索引构建API...")
    
    # 测试数据
    test_data = {
        "urls": [
            "https://docs.python.org/3/tutorial/introduction.html",
            "https://fastapi.tiangolo.com/tutorial/"
        ],
        "documents": [
            "Python是一种高级编程语言，具有简洁的语法和强大的功能。",
            "FastAPI是一个现代、快速的Web框架，用于构建API。"
        ]
    }
    
    try:
        # 发送构建请求
        response = requests.post(
            f"{base_url}/index/build",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 索引构建请求成功")
            print(f"   消息: {result.get('message', 'N/A')}")
            print(f"   状态: {result.get('status', 'N/A')}")
            print(f"   URL数量: {result.get('urls_count', 0)}")
            print(f"   文档数量: {result.get('documents_count', 0)}")
            return True
        else:
            print(f"❌ 索引构建请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到RAG服务")
        print("   请确保服务正在运行: python main.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    base_url = "http://localhost:8000"
    
    print("🏥 测试健康检查...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
            return True
        else:
            print(f"⚠️ 健康检查异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_static_files():
    """测试静态文件访问"""
    base_url = "http://localhost:8000"
    
    print("📄 测试静态文件访问...")
    
    pages = [
        "/web/index-builder.html",
        "/web/index.html",
        "/web/stream.html",
        "/web/welcome.html"
    ]
    
    success_count = 0
    for page in pages:
        try:
            response = requests.get(f"{base_url}{page}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {page} - 访问成功")
                success_count += 1
            else:
                print(f"❌ {page} - 访问失败: {response.status_code}")
        except Exception as e:
            print(f"❌ {page} - 访问错误: {e}")
    
    print(f"📊 静态文件测试结果: {success_count}/{len(pages)} 成功")
    return success_count == len(pages)

def main():
    """主测试函数"""
    print("🚀 开始测试索引构建器功能")
    print("=" * 50)
    
    # 测试健康检查
    health_ok = test_health_check()
    print()
    
    # 测试静态文件
    static_ok = test_static_files()
    print()
    
    # 如果服务正常，测试API
    if health_ok:
        api_ok = test_index_build_api()
        print()
    else:
        api_ok = False
        print("⏭️ 跳过API测试（服务未运行）")
    
    # 总结
    print("=" * 50)
    print("📋 测试总结:")
    print(f"   健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"   静态文件: {'✅ 通过' if static_ok else '❌ 失败'}")
    print(f"   API功能: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if health_ok and static_ok:
        print("\n🎉 索引构建器页面已就绪！")
        print("📱 访问地址:")
        print("   主页: http://localhost:8000/")
        print("   索引构建器: http://localhost:8000/web/index-builder.html")
        print("   标准对话: http://localhost:8000/web/index.html")
        print("   流式对话: http://localhost:8000/web/stream.html")
    else:
        print("\n⚠️ 部分功能异常，请检查服务状态")
    
    return health_ok and static_ok and api_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
