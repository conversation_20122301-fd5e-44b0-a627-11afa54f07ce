#!/bin/bash

# RAG API 部署脚本
# 支持开发环境和生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
RAG API 部署脚本

用法: $0 [选项]

选项:
    -e, --env ENV           部署环境 (dev|prod) [默认: dev]
    -p, --port PORT         服务端口 [默认: 8000]
    -h, --host HOST         服务主机 [默认: 0.0.0.0]
    -w, --workers WORKERS   工作进程数 (仅生产环境) [默认: 4]
    --install-deps          安装依赖
    --build-docker          构建Docker镜像
    --run-docker            运行Docker容器
    --run-tests             运行测试
    --help                  显示此帮助信息

示例:
    $0 --env dev --port 8000                    # 开发环境部署
    $0 --env prod --workers 4                   # 生产环境部署
    $0 --install-deps --run-tests               # 安装依赖并运行测试
    $0 --build-docker --run-docker              # Docker部署

EOF
}

# 默认参数
ENVIRONMENT="dev"
PORT="8000"
HOST="0.0.0.0"
WORKERS="4"
INSTALL_DEPS=false
BUILD_DOCKER=false
RUN_DOCKER=false
RUN_TESTS=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        --install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        --build-docker)
            BUILD_DOCKER=true
            shift
            ;;
        --run-docker)
            RUN_DOCKER=true
            shift
            ;;
        --run-tests)
            RUN_TESTS=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "无效的环境: $ENVIRONMENT (必须是 dev 或 prod)"
    exit 1
fi

log_info "开始部署 RAG API..."
log_info "环境: $ENVIRONMENT"
log_info "端口: $PORT"
log_info "主机: $HOST"

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    log_success "Python版本: $PYTHON_VERSION"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    required_files=("main.py" "rag_service.py" "config.py" "requirements.txt")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "所有必要文件存在"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    if [[ -f ".env" ]]; then
        log_success "找到 .env 文件"
    else
        log_warning ".env 文件不存在"
        if [[ -f ".env.example" ]]; then
            log_info "复制 .env.example 到 .env"
            cp .env.example .env
            log_warning "请编辑 .env 文件设置API密钥"
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        pip3 install -r requirements-dev.txt
    else
        pip3 install -r requirements.txt
    fi
    
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    if [[ -f "run_tests.py" ]]; then
        python3 run_tests.py --type unit
    else
        python3 -m pytest test_*.py -v
    fi
    
    log_success "测试完成"
}

# 构建Docker镜像
build_docker_image() {
    log_info "构建Docker镜像..."
    
    if [[ ! -f "Dockerfile" ]]; then
        log_error "Dockerfile 不存在"
        exit 1
    fi
    
    docker build -t rag-api:latest .
    log_success "Docker镜像构建完成"
}

# 运行Docker容器
run_docker_container() {
    log_info "运行Docker容器..."
    
    # 停止现有容器
    if docker ps -q -f name=rag-api-container &> /dev/null; then
        log_info "停止现有容器..."
        docker stop rag-api-container
        docker rm rag-api-container
    fi
    
    # 运行新容器
    docker run -d \
        --name rag-api-container \
        -p $PORT:8000 \
        --env-file .env \
        -v $(pwd)/logs:/app/logs \
        rag-api:latest
    
    log_success "Docker容器已启动"
    log_info "容器日志: docker logs -f rag-api-container"
}

# 开发环境部署
deploy_development() {
    log_info "部署到开发环境..."
    
    export HOST="$HOST"
    export PORT="$PORT"
    export RELOAD="true"
    export LOG_LEVEL="debug"
    
    log_info "启动开发服务器..."
    python3 main.py
}

# 生产环境部署
deploy_production() {
    log_info "部署到生产环境..."
    
    export HOST="$HOST"
    export PORT="$PORT"
    export WORKERS="$WORKERS"
    export RELOAD="false"
    export LOG_LEVEL="info"
    
    log_info "启动生产服务器..."
    if [[ -f "production.py" ]]; then
        python3 production.py
    else
        # 使用Gunicorn（如果可用）
        if command -v gunicorn &> /dev/null; then
            gunicorn main:app \
                --bind $HOST:$PORT \
                --workers $WORKERS \
                --worker-class uvicorn.workers.UvicornWorker \
                --access-logfile - \
                --log-level info
        else
            # 回退到Uvicorn
            uvicorn main:app \
                --host $HOST \
                --port $PORT \
                --log-level info
        fi
    fi
}

# 主执行流程
main() {
    # 基础检查
    check_python
    check_files
    check_env_vars
    
    # 安装依赖
    if [[ "$INSTALL_DEPS" == true ]]; then
        install_dependencies
    fi
    
    # 运行测试
    if [[ "$RUN_TESTS" == true ]]; then
        run_tests
    fi
    
    # Docker部署
    if [[ "$BUILD_DOCKER" == true ]]; then
        build_docker_image
    fi
    
    if [[ "$RUN_DOCKER" == true ]]; then
        run_docker_container
        exit 0
    fi
    
    # 常规部署
    if [[ "$ENVIRONMENT" == "dev" ]]; then
        deploy_development
    else
        deploy_production
    fi
}

# 信号处理
trap 'log_info "部署被中断"; exit 1' INT TERM

# 执行主函数
main
