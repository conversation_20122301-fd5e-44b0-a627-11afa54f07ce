"""
统一的RAG服务类
整合所有RAG组件，提供简洁的API接口
"""

import os
import json
from typing import List, Dict, Any, Optional, AsyncGenerator, Type, Union, Callable
from langchain_core.pydantic_v1 import BaseModel
# from pprint import pprint  # 暂时不需要
import logging

# 禁用ChromaDB遥测以避免错误
os.environ["ANONYMIZED_TELEMETRY"] = "False"
os.environ["CHROMA_TELEMETRY"] = "False"
os.environ["POSTHOG_DISABLED"] = "True"
os.environ["DO_NOT_TRACK"] = "1"

# 禁用ChromaDB的日志记录以减少噪音
import warnings
import logging
warnings.filterwarnings("ignore", category=UserWarning, module="chromadb")
warnings.filterwarnings("ignore", category=UserWarning, module="posthog")

# 禁用ChromaDB和posthog的日志记录
logging.getLogger("chromadb").setLevel(logging.ERROR)
logging.getLogger("posthog").setLevel(logging.ERROR)
logging.getLogger("chromadb.telemetry").setLevel(logging.CRITICAL)

# 自定义模块
from exceptions import (
    RAGException, ConfigurationError, APIKeyError, VectorStoreError,
    IndexNotBuiltError, DocumentProcessingError, RetrievalError,
    GenerationError, WebSearchError, ValidationError, handle_openai_error,
    handle_tavily_error, handle_chroma_error, handle_document_loading_error
)
from logger import LoggerMixin, log_execution_time, log_function_call

# LangChain imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import WebBaseLoader
from langchain_community.vectorstores import Chroma
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain.schema import Document
from langchain import hub
from langchain_community.tools.tavily_search import TavilySearchResults

# LangGraph imports
from langgraph.graph import END, StateGraph, START
from typing_extensions import TypedDict

# Pydantic imports
from pydantic import BaseModel, Field
from typing import Literal

# 添加Ollama相关导入
from langchain_community.llms import Ollama
from langchain_community.embeddings import OllamaEmbeddings
from langchain_core.prompts import ChatPromptTemplate

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GraphState(TypedDict):
    """
    图状态定义
    """
    question: str
    generation: str
    documents: List[str]


class RouteQuery(BaseModel):
    """路由查询数据模型"""
    datasource: Literal["vectorstore", "web_search"] = Field(
        ...,
        description="Given a user question choose to route it to web search or a vectorstore.",
    )


class GradeDocuments(BaseModel):
    """文档相关性评分数据模型"""
    binary_score: str = Field(
        description="Documents are relevant to the question, 'yes' or 'no'"
    )


class GradeHallucinations(BaseModel):
    """幻觉检测数据模型"""
    binary_score: str = Field(
        description="Answer is grounded in the facts, 'yes' or 'no'"
    )


class GradeAnswer(BaseModel):
    """答案质量评分数据模型"""
    binary_score: str = Field(
        description="Answer addresses the question, 'yes' or 'no'"
    )


import re
import json

def create_structured_output_parser(llm, pydantic_schema: Type[BaseModel]):
    """为Ollama模型创建结构化输出解析器"""
    def parse_output(text: str) -> Dict:
        """尝试将文本解析为JSON"""
        # 尝试提取JSON部分
        try:
            # 查找可能的JSON部分
            json_match = re.search(r'```json\n(.*?)\n```', text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个文本
                json_str = text

            # 解析JSON
            data = json.loads(json_str)
            # 验证结构
            return pydantic_schema.parse_obj(data)
        except Exception as e:
            logger.error(f"解析结构化输出失败: {str(e)}")
            # 创建默认响应
            if hasattr(pydantic_schema, 'binary_score'):
                return pydantic_schema(binary_score="yes")
            elif hasattr(pydantic_schema, 'datasource'):
                return pydantic_schema(datasource="vectorstore")
            else:
                # 创建一个空对象
                return pydantic_schema()

    # 创建提示模板 - 转义JSON中的花括号
    try:
        # 使用新的方法
        schema_dict = pydantic_schema.model_json_schema()
        schema_json = json.dumps(schema_dict, indent=2)
    except AttributeError:
        # 回退到旧方法
        schema_json = pydantic_schema.schema_json()

    # 转义JSON中的花括号，避免被LangChain误认为是变量
    escaped_schema = schema_json.replace("{", "{{").replace("}", "}}")

    system_prompt = f"""你需要根据用户输入生成符合以下JSON格式的输出:
    {escaped_schema}

    只返回有效的JSON对象，不要有任何其他文本。将JSON包装在```json和```标记中。"""

    prompt_template = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("human", "{input}")
    ])

    # 创建链
    chain = prompt_template | llm | StrOutputParser()

    # 创建包装函数
    def structured_output_wrapper(input_data):
        result = chain.invoke({"input": input_data})
        return parse_output(result)

    return structured_output_wrapper


class RAGService(LoggerMixin):
    """统一的RAG服务类"""

    def __init__(self, config=None):
        """
        初始化RAG服务

        Args:
            config: RAG配置对象，如果为None则使用默认配置
        """
        # 导入配置
        if config is None:
            from config import get_rag_config
            config = get_rag_config()

        self.config = config

        # 设置API密钥
        if not self.config.use_local_model:
            if config.openai_api_key:
                os.environ["OPENAI_API_KEY"] = config.openai_api_key
            if config.tavily_api_key:
                os.environ["TAVILY_API_KEY"] = config.tavily_api_key

        self.model_name = config.model_name
        self.chunk_size = config.chunk_size
        self.chunk_overlap = config.chunk_overlap

        # 初始化组件
        self._init_components()
        self._init_chains()
        self._init_graph()

        logger.info("RAG服务初始化完成")

    def _initialize_vectorstore(self):
        """初始化向量存储，检查是否已存在数据"""
        import os

        self.vectorstore = None
        self.retriever = None

        # 检查是否配置了持久化目录
        if not self.config.vectorstore_persist_directory:
            self.logger.info("未配置持久化目录，向量存储将在内存中运行")
            return

        persist_dir = self.config.vectorstore_persist_directory

        # 检查持久化目录是否存在且包含数据
        if os.path.exists(persist_dir) and os.listdir(persist_dir):
            self.logger.info(f"发现现有向量数据库: {persist_dir}")
            try:
                # 尝试加载现有的向量存储
                self.vectorstore = Chroma(
                    persist_directory=persist_dir,
                    collection_name=self.config.vectorstore_collection_name,
                    embedding_function=self.embeddings
                )

                # 检查集合是否有数据
                collection = self.vectorstore._collection
                doc_count = collection.count()

                if doc_count > 0:
                    self.logger.info(f"成功加载现有向量数据库，包含 {doc_count} 个文档")
                    self.retriever = self.vectorstore.as_retriever(search_kwargs={"k": self.config.retrieval_k})
                    return
                else:
                    self.logger.info("向量数据库为空，需要构建索引")

            except Exception as e:
                self.logger.warning(f"加载现有向量数据库失败: {e}")
                self.logger.info("将创建新的向量数据库")
        else:
            self.logger.info(f"向量数据库目录不存在或为空: {persist_dir}")

        # 如果没有现有数据，尝试使用默认URL构建初始索引
        if self.config.default_urls:
            self.logger.info("使用默认URL构建初始索引...")
            try:
                self.build_index()
            except Exception as e:
                self.logger.error(f"构建初始索引失败: {e}")
                self.logger.info("服务将在没有向量索引的情况下启动")
        else:
            self.logger.info("未配置默认URL，服务将在没有向量索引的情况下启动")

    def _rerank_documents(self, query: str, documents: List[Document], top_k: int = None) -> List[Document]:
        """使用重排序模型对文档进行重排序"""
        if not self.config.use_local_model or not hasattr(self, 'reranker') or not self.reranker:
            # 如果没有重排序模型，直接返回原文档
            return documents[:top_k] if top_k else documents

        if not documents:
            return documents

        try:
            self.logger.info(f"使用重排序模型对 {len(documents)} 个文档进行重排序")

            # 为每个文档计算相关性分数
            scored_docs = []
            for doc in documents:
                # 构建简洁的重排序提示
                rerank_prompt = f"""Query: {query}
Document: {doc.page_content[:300]}

Relevance score (0-10): """

                try:
                    score_response = self.reranker.invoke(rerank_prompt)
                    # 尝试提取数字分数
                    import re
                    score_match = re.search(r'\b([0-9]|10)\b', str(score_response))
                    score = float(score_match.group(1)) if score_match else 5.0
                except Exception as e:
                    self.logger.warning(f"重排序评分失败: {e}")
                    score = 5.0  # 默认分数

                scored_docs.append((doc, score))

            # 按分数排序
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            reranked_docs = [doc for doc, _ in scored_docs]

            # 验证返回的文档格式
            for i, doc in enumerate(reranked_docs):
                if not hasattr(doc, 'page_content'):
                    self.logger.error(f"重排序后文档 {i} 格式错误: {type(doc)}")

            self.logger.info(f"重排序完成，分数范围: {scored_docs[-1][1]:.1f} - {scored_docs[0][1]:.1f}")

            return reranked_docs[:top_k] if top_k else reranked_docs

        except Exception as e:
            self.logger.error(f"重排序失败: {e}")
            return documents[:top_k] if top_k else documents

    def _safe_create_chroma(self, **kwargs):
        """安全创建ChromaDB实例，处理遥测错误"""
        try:
            # 临时禁用日志记录以避免遥测错误输出
            import logging
            chroma_logger = logging.getLogger('chromadb')
            original_level = chroma_logger.level
            chroma_logger.setLevel(logging.ERROR)

            try:
                vectorstore = Chroma.from_documents(**kwargs)
                return vectorstore
            finally:
                # 恢复原始日志级别
                chroma_logger.setLevel(original_level)

        except Exception as e:
            # 如果遥测错误，尝试忽略并继续
            if "capture() takes 1 positional argument" in str(e):
                logger.warning("检测到ChromaDB遥测错误，尝试忽略并继续...")
                try:
                    # 再次尝试创建，这次忽略遥测错误
                    vectorstore = Chroma.from_documents(**kwargs)
                    return vectorstore
                except Exception as e2:
                    logger.error(f"ChromaDB创建失败: {e2}")
                    raise e2
            else:
                raise e

    def _init_components(self):
        """初始化基础组件"""
        # 根据配置选择使用本地模型还是OpenAI模型
        if self.config.use_local_model:
            # 使用专门的Ollama模型
            self.logger.info(f"使用本地Ollama模型:")
            self.logger.info(f"  LLM模型: {self.config.ollama_model_name}")
            self.logger.info(f"  嵌入模型: {self.config.ollama_embedding_model}")
            self.logger.info(f"  重排序模型: {self.config.ollama_reranker_model}")

            # 使用专门的嵌入模型
            self.embeddings = OllamaEmbeddings(
                base_url=self.config.ollama_base_url,
                model=self.config.ollama_embedding_model
            )

            # 使用专门的LLM模型，添加超时和优化配置
            self.llm = Ollama(
                base_url=self.config.ollama_base_url,
                model=self.config.ollama_model_name,
                temperature=self.config.temperature,
                timeout=60,  # 60秒超时
                num_predict=256,  # 限制输出长度
                top_k=10,  # 减少采样范围
                top_p=0.9,  # 优化采样
                repeat_penalty=1.1  # 减少重复
            )

            # 初始化重排序模型（如果需要）
            self.reranker = Ollama(
                base_url=self.config.ollama_base_url,
                model=self.config.ollama_reranker_model,
                temperature=0.0,  # 重排序使用较低温度
                timeout=60,  # 增加超时时间
                num_predict=10,  # 限制输出长度，只需要分数
                top_k=5,  # 减少采样
                top_p=0.8
            )
        else:
            # 使用OpenAI的嵌入模型
            self.logger.info(f"使用OpenAI模型: {self.model_name}")
            self.embeddings = OpenAIEmbeddings()
            self.llm = ChatOpenAI(
                model=self.model_name,
                temperature=self.config.temperature,
                request_timeout=30,  # 30秒超时
                max_retries=2  # 最多重试2次
            )
            self.reranker = None  # OpenAI模式下不使用重排序

        # 文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )

        # 网络搜索工具
        self.web_search_tool = TavilySearchResults(k=self.config.web_search_k)

        # 初始化向量存储
        self._initialize_vectorstore()

    def _extract_text_from_output(self, output):
        """从不同类型的输出中提取文本"""
        if hasattr(output, 'content'):
            return output.content.strip().lower()
        elif isinstance(output, str):
            return output.strip().lower()
        else:
            return str(output).strip().lower()

    def _init_chains(self):
        """初始化各种链"""
        # 路由链 - 使用简单的文本输出解析
        route_system = """You are an expert at routing a user question to a vectorstore or web search.
        The vectorstore contains documents related to agents, prompt engineering, and adversarial attacks.
        Use the vectorstore for questions on these topics. Otherwise, use web-search.

        Respond with EXACTLY one word: either "vectorstore" or "websearch"."""

        route_prompt = ChatPromptTemplate.from_messages([
            ("system", route_system),
            ("human", "{question}"),
        ])

        def parse_route_output(output):
            """解析路由输出"""
            text = self._extract_text_from_output(output)
            if "vectorstore" in text:
                return RouteQuery(datasource="vectorstore")
            else:
                return RouteQuery(datasource="websearch")

        self.question_router = route_prompt | self.llm | parse_route_output

        # 文档相关性评分链 - 使用简单的文本输出解析
        grade_system = """You are a grader assessing relevance of a retrieved document to a user question.
        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant.
        It does not need to be a stringent test. The goal is to filter out erroneous retrievals.

        Respond with EXACTLY one word: either "yes" or "no" to indicate whether the document is relevant to the question."""

        grade_prompt = ChatPromptTemplate.from_messages([
            ("system", grade_system),
            ("human", "Retrieved document: \n\n {document} \n\n User question: {question}"),
        ])

        def parse_grade_output(output):
            """解析评分输出"""
            text = self._extract_text_from_output(output)
            if "yes" in text:
                return GradeDocuments(binary_score="yes")
            else:
                return GradeDocuments(binary_score="no")

        self.retrieval_grader = grade_prompt | self.llm | parse_grade_output

        # 幻觉检测链 - 使用简单的文本输出解析
        hallucination_system = """You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts.

        Respond with EXACTLY one word: either "yes" or "no". 'Yes' means that the answer is grounded in / supported by the set of facts."""

        hallucination_prompt = ChatPromptTemplate.from_messages([
            ("system", hallucination_system),
            ("human", "Set of facts: \n\n {documents} \n\n LLM generation: {generation}"),
        ])

        def parse_hallucination_output(output):
            """解析幻觉检测输出"""
            text = self._extract_text_from_output(output)
            if "yes" in text:
                return GradeHallucinations(binary_score="yes")
            else:
                return GradeHallucinations(binary_score="no")

        self.hallucination_grader = hallucination_prompt | self.llm | parse_hallucination_output

        # 答案质量评分链 - 使用简单的文本输出解析
        answer_system = """You are a grader assessing whether an answer addresses / resolves a question.

        Respond with EXACTLY one word: either "yes" or "no". 'Yes' means that the answer resolves the question."""

        answer_prompt = ChatPromptTemplate.from_messages([
            ("system", answer_system),
            ("human", "User question: \n\n {question} \n\n LLM generation: {generation}"),
        ])

        def parse_answer_output(output):
            """解析答案质量输出"""
            text = self._extract_text_from_output(output)
            if "yes" in text:
                return GradeAnswer(binary_score="yes")
            else:
                return GradeAnswer(binary_score="no")

        self.answer_grader = answer_prompt | self.llm | parse_answer_output

        # 问题重写链
        rewrite_system = """You a question re-writer that converts an input question to a better version that is optimized
        for vectorstore retrieval. Look at the input and try to reason about the underlying semantic intent / meaning."""
        rewrite_prompt = ChatPromptTemplate.from_messages([
            ("system", rewrite_system),
            ("human", "Here is the initial question: \n\n {question} \n Formulate an improved question."),
        ])
        self.question_rewriter = rewrite_prompt | self.llm | StrOutputParser()

        # RAG生成链
        try:
            prompt = hub.pull("rlm/rag-prompt")
        except:
            # 如果无法从hub获取，使用默认提示
            prompt = ChatPromptTemplate.from_messages([
                ("system", "You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise."),
                ("human", "Context: {context}\n\nQuestion: {question}")
            ])
        self.rag_chain = prompt | self.llm | StrOutputParser()

    @log_execution_time
    def build_index(self, urls: List[str] = None, documents: List[str] = None, append: bool = False):
        """
        构建或更新向量索引

        Args:
            urls: 要索引的URL列表
            documents: 要索引的文档内容列表
            append: 是否追加到现有索引（True）还是重建索引（False）
        """
        if urls is None and documents is None:
            # 使用配置中的默认URL
            urls = self.config.default_urls

        self.logger.info(f"开始{'追加' if append else '构建'}索引，URLs: {len(urls) if urls else 0}, 文档: {len(documents) if documents else 0}")

        try:
            if urls:
                # 从URL加载文档
                docs_list = []
                for url in urls:
                    try:
                        self.logger.debug(f"加载URL: {url}")
                        loader_docs = WebBaseLoader(url).load()
                        docs_list.extend(loader_docs)
                    except Exception as e:
                        raise handle_document_loading_error(e, url)
            else:
                # 从文档内容创建Document对象
                docs_list = [Document(page_content=doc) for doc in documents]

            if not docs_list:
                raise DocumentProcessingError("没有找到可处理的文档")

            self.logger.info(f"成功加载 {len(docs_list)} 个文档")

            # 分割文档
            try:
                doc_splits = self.text_splitter.split_documents(docs_list)
                self.logger.info(f"文档分割完成，共 {len(doc_splits)} 个块")
            except Exception as e:
                raise DocumentProcessingError(f"文档分割失败: {str(e)}")

            # 创建或更新向量存储
            try:
                if append and self.vectorstore is not None:
                    # 追加模式：向现有向量存储添加文档
                    self.logger.info("追加文档到现有向量存储")
                    self.vectorstore.add_documents(doc_splits)

                    # 获取更新后的文档数量
                    collection = self.vectorstore._collection
                    total_count = collection.count()
                    self.logger.info(f"成功追加 {len(doc_splits)} 个文档块，总计 {total_count} 个文档")

                else:
                    # 重建模式：创建新的向量存储
                    vectorstore_kwargs = {
                        "documents": doc_splits,
                        "collection_name": self.config.vectorstore_collection_name,
                        "embedding": self.embeddings,
                    }

                    # 如果配置了持久化目录，添加到参数中
                    if self.config.vectorstore_persist_directory:
                        vectorstore_kwargs["persist_directory"] = self.config.vectorstore_persist_directory

                    # 使用安全的ChromaDB初始化
                    self.vectorstore = self._safe_create_chroma(**vectorstore_kwargs)
                    self.retriever = self.vectorstore.as_retriever(search_kwargs={"k": self.config.retrieval_k})

                    self.logger.info(f"成功构建索引，包含 {len(doc_splits)} 个文档块")

            except Exception as e:
                raise handle_chroma_error(e)

        except (DocumentProcessingError, VectorStoreError):
            # 重新抛出已处理的异常
            raise
        except Exception as e:
            # 处理其他未预期的异常
            self.logger.error(f"构建索引时发生未预期错误: {str(e)}", exc_info=True)
            raise VectorStoreError(f"构建索引失败: {str(e)}")

    def format_docs(self, docs):
        """格式化文档"""
        # 添加调试信息
        self.logger.debug(f"format_docs 接收到 {len(docs)} 个文档")
        for i, doc in enumerate(docs):
            self.logger.debug(f"文档 {i} 类型: {type(doc)}")
            if hasattr(doc, 'page_content'):
                self.logger.debug(f"文档 {i} 内容长度: {len(doc.page_content)}")
            else:
                self.logger.error(f"文档 {i} 没有 page_content 属性: {doc}")

        # 过滤和处理文档
        valid_docs = []
        for doc in docs:
            if hasattr(doc, 'page_content'):
                valid_docs.append(doc)
            elif isinstance(doc, tuple) and len(doc) >= 1 and hasattr(doc[0], 'page_content'):
                # 如果是 tuple，取第一个元素（可能是 (doc, score) 格式）
                self.logger.warning(f"检测到 tuple 格式文档，提取第一个元素")
                valid_docs.append(doc[0])
            else:
                self.logger.error(f"无效的文档格式: {type(doc)} - {doc}")

        return "\n\n".join(doc.page_content for doc in valid_docs)

    # 图节点函数
    def retrieve(self, state):
        """检索文档"""
        self.logger.info("---RETRIEVE---")
        question = state["question"]

        if not self.retriever:
            raise IndexNotBuiltError()

        try:
            self.logger.debug(f"检索问题: {question}")
            documents = self.retriever.invoke(question)
            self.logger.info(f"检索到 {len(documents)} 个文档")

            # 如果使用本地模型且有重排序模型，进行重排序
            if self.config.use_local_model and hasattr(self, 'reranker') and self.reranker:
                self.logger.debug(f"重排序前文档类型: {[type(doc) for doc in documents[:3]]}")
                documents = self._rerank_documents(question, documents, top_k=self.config.retrieval_k)
                self.logger.info(f"重排序后保留 {len(documents)} 个文档")
                self.logger.debug(f"重排序后文档类型: {[type(doc) for doc in documents[:3]]}")

            return {"documents": documents, "question": question}
        except Exception as e:
            self.logger.error(f"文档检索失败: {str(e)}")
            raise RetrievalError(f"文档检索失败: {str(e)}")

    def generate(self, state):
        """生成答案"""
        self.logger.info("---GENERATE---")
        question = state["question"]
        documents = state["documents"]

        try:
            docs_txt = self.format_docs(documents)
            self.logger.debug(f"生成答案，上下文长度: {len(docs_txt)} 字符")

            generation = self.rag_chain.invoke({"context": docs_txt, "question": question})
            self.logger.info("答案生成成功")
            return {"documents": documents, "question": question, "generation": generation}

        except Exception as e:
            self.logger.error(f"答案生成失败: {str(e)}")
            raise handle_openai_error(e)

    def grade_documents(self, state):
        """评估文档相关性"""
        logger.info("---CHECK DOCUMENT RELEVANCE TO QUESTION---")
        question = state["question"]
        documents = state["documents"]

        filtered_docs = []
        for d in documents:
            score = self.retrieval_grader.invoke(
                {"question": question, "document": d.page_content}
            )
            grade = score.binary_score
            if grade == "yes":
                logger.info("---GRADE: DOCUMENT RELEVANT---")
                filtered_docs.append(d)
            else:
                logger.info("---GRADE: DOCUMENT NOT RELEVANT---")
                continue
        return {"documents": filtered_docs, "question": question}

    def transform_query(self, state):
        """转换查询"""
        logger.info("---TRANSFORM QUERY---")
        question = state["question"]
        documents = state["documents"]

        better_question = self.question_rewriter.invoke({"question": question})
        return {"documents": documents, "question": better_question}

    def web_search(self, state):
        """网络搜索"""
        self.logger.info("---WEB SEARCH---")
        question = state["question"]

        try:
            self.logger.debug(f"网络搜索问题: {question}")
            docs = self.web_search_tool.invoke({"query": question})

            if not docs:
                self.logger.warning("网络搜索未返回结果")
                web_results = Document(page_content="未找到相关网络信息")
            else:
                web_results = "\n".join([d["content"] for d in docs])
                web_results = Document(page_content=web_results)
                self.logger.info(f"网络搜索成功，获得 {len(docs)} 个结果")

            return {"documents": web_results, "question": question}

        except Exception as e:
            self.logger.error(f"网络搜索失败: {str(e)}")
            raise handle_tavily_error(e)

    # 边缘条件函数
    def route_question(self, state):
        """路由问题"""
        logger.info("---ROUTE QUESTION---")
        question = state["question"]

        try:
            logger.info(f"正在路由问题: {question[:100]}...")

            # 首先尝试简单的关键词路由作为快速备选
            if self._should_use_web_search(question):
                logger.info("---ROUTE QUESTION TO WEB SEARCH (关键词匹配)---")
                return "web_search"

            # 尝试使用LLM路由，使用线程池超时机制
            from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

            try:
                with ThreadPoolExecutor() as executor:
                    future = executor.submit(self.question_router.invoke, {"question": question})

                    try:
                        source = future.result(timeout=15)  # 15秒超时
                        logger.info(f"路由结果: {source.datasource}")

                        if source.datasource == "web_search":
                            logger.info("---ROUTE QUESTION TO WEB SEARCH---")
                            return "web_search"
                        elif source.datasource == "vectorstore":
                            logger.info("---ROUTE QUESTION TO RAG---")
                            return "vectorstore"
                        else:
                            logger.warning(f"未知的路由结果: {source.datasource}，默认使用vectorstore")
                            return "vectorstore"

                    except FutureTimeoutError:
                        logger.warning("LLM路由超时 (>15秒)")

                        # 使用备用的关键词路由
                        if self._should_use_web_search(question):
                            logger.info("---ROUTE QUESTION TO WEB SEARCH (备用路由)---")
                            return "web_search"
                        else:
                            logger.info("---ROUTE QUESTION TO RAG (备用路由)---")
                            return "vectorstore"

            except Exception as e:
                logger.warning(f"LLM路由失败: {str(e)}")

                # 使用备用的关键词路由
                if self._should_use_web_search(question):
                    logger.info("---ROUTE QUESTION TO WEB SEARCH (备用路由)---")
                    return "web_search"
                else:
                    logger.info("---ROUTE QUESTION TO RAG (备用路由)---")
                    return "vectorstore"

        except Exception as e:
            logger.error(f"路由问题时出错: {str(e)}")
            logger.info("路由失败，默认使用vectorstore")
            return "vectorstore"

    def _should_use_web_search(self, question):
        """基于关键词判断是否应该使用网络搜索"""
        web_keywords = [
            "今天", "明天", "昨天", "现在", "最新", "新闻", "天气", "股价",
            "实时", "当前", "最近", "今年", "2024", "2025", "价格", "报价"
        ]

        question_lower = question.lower()
        for keyword in web_keywords:
            if keyword in question_lower:
                return True
        return False

    def decide_to_generate(self, state):
        """决定是否生成答案"""
        logger.info("---ASSESS GRADED DOCUMENTS---")
        filtered_documents = state["documents"]

        if not filtered_documents:
            logger.info("---DECISION: ALL DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---")
            return "transform_query"
        else:
            logger.info("---DECISION: GENERATE---")
            return "generate"

    def grade_generation_v_documents_and_question(self, state):
        """评估生成质量"""
        logger.info("---CHECK HALLUCINATIONS---")
        question = state["question"]
        documents = state["documents"]
        generation = state["generation"]

        score = self.hallucination_grader.invoke(
            {"documents": documents, "generation": generation}
        )
        grade = score.binary_score

        if grade == "yes":
            logger.info("---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---")
            logger.info("---GRADE GENERATION vs QUESTION---")

            score = self.answer_grader.invoke({"question": question, "generation": generation})

            grade = score.binary_score
            if grade == "yes":
                logger.info("---DECISION: GENERATION ADDRESSES QUESTION---")
                return "useful"
            else:
                logger.info("---DECISION: GENERATION DOES NOT ADDRESS QUESTION---")
                return "not useful"
        else:
            logger.info("---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---")
            return "not supported"

    def _init_graph(self):
        """初始化LangGraph工作流"""
        workflow = StateGraph(GraphState)

        # 定义节点
        workflow.add_node("web_search", self.web_search)
        workflow.add_node("retrieve", self.retrieve)
        workflow.add_node("grade_documents", self.grade_documents)
        workflow.add_node("generate", self.generate)
        workflow.add_node("transform_query", self.transform_query)

        # 构建图
        workflow.add_conditional_edges(
            START,
            self.route_question,
            {
                "web_search": "web_search",
                "vectorstore": "retrieve",
            },
        )
        workflow.add_edge("web_search", "generate")
        workflow.add_edge("retrieve", "grade_documents")
        workflow.add_conditional_edges(
            "grade_documents",
            self.decide_to_generate,
            {
                "transform_query": "transform_query",
                "generate": "generate",
            },
        )
        workflow.add_edge("transform_query", "retrieve")
        workflow.add_conditional_edges(
            "generate",
            self.grade_generation_v_documents_and_question,
            {
                "not supported": "generate",
                "useful": END,
                "not useful": "transform_query",
            },
        )

        # 编译图
        self.app = workflow.compile()

    @log_execution_time
    def query(self, question: str) -> str:
        """
        查询RAG系统

        Args:
            question: 用户问题

        Returns:
            生成的答案
        """
        if not question or not question.strip():
            raise ValidationError("问题不能为空")

        self.logger.info(f"开始处理查询: {question[:100]}...")

        try:
            inputs = {"question": question.strip()}
            result = None

            for output in self.app.stream(inputs):
                for key, value in output.items():
                    self.logger.debug(f"节点 '{key}' 完成")
                    result = value

            if result and "generation" in result:
                answer = result["generation"]
                self.logger.info("查询处理成功")
                return answer
            else:
                self.logger.warning("未能生成有效答案")
                return "抱歉，无法生成答案，请尝试重新表述您的问题。"

        except (RAGException, IndexNotBuiltError, RetrievalError, GenerationError, WebSearchError):
            # 重新抛出已处理的异常
            raise
        except Exception as e:
            self.logger.error(f"查询处理时发生未预期错误: {str(e)}", exc_info=True)
            raise RAGException(f"查询处理失败: {str(e)}")

    async def query_stream(self, question: str) -> AsyncGenerator[str, None]:
        """
        流式查询RAG系统

        Args:
            question: 用户问题

        Yields:
            生成的答案片段
        """
        try:
            import asyncio
            inputs = {"question": question}

            # 首先发送状态更新
            yield "🔍 正在分析问题..."
            await asyncio.sleep(0.5)

            result = None
            for output in self.app.stream(inputs):
                for key, value in output.items():
                    logger.info(f"Node '{key}' completed")

                    # 根据不同节点提供状态更新
                    if key == "web_search":
                        yield "\n🌐 正在搜索网络信息..."
                        await asyncio.sleep(0.3)
                    elif key == "retrieve":
                        yield "\n📚 正在检索相关文档..."
                        await asyncio.sleep(0.3)
                    elif key == "grade_documents":
                        yield "\n📋 正在评估文档相关性..."
                        await asyncio.sleep(0.3)
                    elif key == "transform_query":
                        yield "\n🔄 正在优化查询..."
                        await asyncio.sleep(0.3)
                    elif key == "generate" and "generation" in value:
                        yield "\n\n💭 正在生成回答...\n\n"
                        await asyncio.sleep(0.5)

                        # 流式输出生成的内容
                        generation = value["generation"]

                        # 按句子分割，提供更自然的流式体验
                        sentences = generation.replace('. ', '.|').replace('? ', '?|').replace('! ', '!|').split('|')

                        for sentence in sentences:
                            if sentence.strip():
                                # 按词分割每个句子
                                words = sentence.strip().split()
                                for i, word in enumerate(words):
                                    if i == 0:
                                        yield word
                                    else:
                                        yield " " + word
                                    await asyncio.sleep(0.03)  # 更快的打字效果

                                # 句子结束后稍作停顿
                                if sentence.strip().endswith(('.', '?', '!')):
                                    await asyncio.sleep(0.2)

                        result = value
                        break

            if result is None:
                yield "\n\n❌ 抱歉，无法生成回答。"

        except Exception as e:
            logger.error(f"流式查询失败: {str(e)}")
            yield f"\n\n❌ 错误: {str(e)}"

    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态

        Returns:
            服务状态信息
        """
        return {
            "model_name": self.model_name,
            "chunk_size": self.chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "vectorstore_ready": self.vectorstore is not None,
            "retriever_ready": self.retriever is not None,
        }













