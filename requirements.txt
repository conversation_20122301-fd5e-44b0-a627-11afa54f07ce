# Web框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# 配置管理
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0

# Lang<PERSON>hain核心
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.10
langchain-openai>=0.0.5

# LangGraph
langgraph>=0.0.20

# 向量数据库
chromadb>=0.4.0

# OpenAI
openai>=1.0.0

# 网络搜索
tavily-python>=0.3.0

# 文档处理
beautifulsoup4>=4.12.0
lxml>=4.9.0
html2text>=2020.1.16

# 文本处理
tiktoken>=0.5.0

# HTTP客户端
httpx>=0.25.0
requests>=2.31.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0

# 异步支持
asyncio-mqtt>=0.13.0

# 日志和监控
structlog>=23.2.0

# 开发工具（可选）
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# 生产环境（可选）
gunicorn>=21.2.0
