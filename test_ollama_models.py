#!/usr/bin/env python3
"""
测试Ollama三个专门模型的配置
"""

import time
import requests
import json

def test_ollama_models():
    """测试Ollama模型可用性"""
    print("🔍 测试Ollama模型可用性...")
    
    ollama_url = "http://localhost:11434"
    models = [
        "qwen3:0.6b",
        "dengcao/Qwen3-Embedding-0.6B:Q8_0",
        "dengcao/Qwen3-Reranker-0.6B:Q8_0"
    ]
    
    try:
        # 获取已安装的模型列表
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models_info = response.json()
            installed_models = [model.get('name', '') for model in models_info.get('models', [])]
            
            print(f"   已安装模型数量: {len(installed_models)}")
            
            for model in models:
                if any(model in installed for installed in installed_models):
                    print(f"   ✅ {model} - 已安装")
                else:
                    print(f"   ❌ {model} - 未安装")
                    return False
            
            return True
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 模型检查失败: {e}")
        return False

def test_llm_model():
    """测试LLM模型"""
    print("\n🤖 测试LLM模型 (qwen3:0.6b)...")
    
    ollama_url = "http://localhost:11434"
    model_name = "qwen3:0.6b"
    
    test_prompt = "Hello, respond with just 'OK'"
    
    try:
        start_time = time.time()
        
        payload = {
            "model": model_name,
            "prompt": test_prompt,
            "stream": False
        }
        
        response = requests.post(
            f"{ollama_url}/api/generate",
            json=payload,
            timeout=30
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            print(f"   ✅ LLM响应成功 (耗时: {end_time - start_time:.2f}秒)")
            print(f"   响应: {response_text[:50]}...")
            return True
        else:
            print(f"   ❌ LLM响应失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ LLM测试失败: {e}")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print("\n📊 测试嵌入模型 (dengcao/Qwen3-Embedding-0.6B:Q8_0)...")
    
    try:
        from langchain_community.embeddings import OllamaEmbeddings
        
        embeddings = OllamaEmbeddings(
            base_url="http://localhost:11434",
            model="dengcao/Qwen3-Embedding-0.6B:Q8_0"
        )
        
        test_text = "This is a test sentence for embedding."
        
        start_time = time.time()
        embedding_vector = embeddings.embed_query(test_text)
        end_time = time.time()
        
        print(f"   ✅ 嵌入模型响应成功 (耗时: {end_time - start_time:.2f}秒)")
        print(f"   向量维度: {len(embedding_vector)}")
        print(f"   向量示例: [{embedding_vector[0]:.4f}, {embedding_vector[1]:.4f}, ...]")
        return True
        
    except Exception as e:
        print(f"   ❌ 嵌入模型测试失败: {e}")
        return False

def test_reranker_model():
    """测试重排序模型"""
    print("\n🔄 测试重排序模型 (dengcao/Qwen3-Reranker-0.6B:Q8_0)...")
    
    ollama_url = "http://localhost:11434"
    model_name = "dengcao/Qwen3-Reranker-0.6B:Q8_0"
    
    test_prompt = """Query: What is machine learning?
Document: Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data.

Rate the relevance of this document to the query on a scale of 0-10.
Respond with only a number between 0 and 10."""
    
    try:
        start_time = time.time()
        
        payload = {
            "model": model_name,
            "prompt": test_prompt,
            "stream": False
        }
        
        response = requests.post(
            f"{ollama_url}/api/generate",
            json=payload,
            timeout=30
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            print(f"   ✅ 重排序模型响应成功 (耗时: {end_time - start_time:.2f}秒)")
            print(f"   响应: {response_text[:50]}...")
            
            # 尝试提取分数
            import re
            score_match = re.search(r'\b([0-9]|10)\b', response_text)
            if score_match:
                score = score_match.group(1)
                print(f"   提取的分数: {score}")
            
            return True
        else:
            print(f"   ❌ 重排序模型响应失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 重排序模型测试失败: {e}")
        return False

def test_rag_service_integration():
    """测试RAG服务集成"""
    print("\n🔗 测试RAG服务集成...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        
        print("   正在初始化RAG服务...")
        rag_service = RAGService(config)
        
        print("   ✅ RAG服务初始化成功")
        print(f"   LLM模型: {config.ollama_model_name}")
        print(f"   嵌入模型: {config.ollama_embedding_model}")
        print(f"   重排序模型: {config.ollama_reranker_model}")
        
        # 测试重排序功能
        from langchain.schema import Document
        
        test_docs = [
            Document(page_content="Machine learning is a subset of AI.", metadata={"source": "doc1"}),
            Document(page_content="The weather is sunny today.", metadata={"source": "doc2"}),
            Document(page_content="Python is a programming language.", metadata={"source": "doc3"})
        ]
        
        query = "What is machine learning?"
        
        print(f"\n   测试重排序功能...")
        print(f"   查询: {query}")
        print(f"   文档数量: {len(test_docs)}")
        
        start_time = time.time()
        reranked_docs = rag_service._rerank_documents(query, test_docs, top_k=2)
        end_time = time.time()
        
        print(f"   ✅ 重排序完成 (耗时: {end_time - start_time:.2f}秒)")
        print(f"   重排序后文档数量: {len(reranked_docs)}")
        
        for i, doc in enumerate(reranked_docs, 1):
            print(f"   {i}. {doc.page_content[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ RAG服务集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Ollama三模型配置测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("模型可用性检查", test_ollama_models),
        ("LLM模型测试", test_llm_model),
        ("嵌入模型测试", test_embedding_model),
        ("重排序模型测试", test_reranker_model),
        ("RAG服务集成测试", test_rag_service_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 所有模型配置正确！")
        print("\n📋 模型分工:")
        print("   🤖 qwen3:0.6b - 文本生成和对话")
        print("   📊 dengcao/Qwen3-Embedding-0.6B:Q8_0 - 文本嵌入")
        print("   🔄 dengcao/Qwen3-Reranker-0.6B:Q8_0 - 文档重排序")
        
        print("\n💡 优势:")
        print("   - 专门模型性能更好")
        print("   - 嵌入质量更高")
        print("   - 重排序提升检索精度")
        print("   - 完全本地化部署")
    else:
        print("\n⚠️  部分模型配置有问题")
        print("\n🔧 解决方案:")
        print("   1. 确保所有模型已安装:")
        print("      ollama pull qwen3:0.6b")
        print("      ollama pull dengcao/Qwen3-Embedding-0.6B:Q8_0")
        print("      ollama pull dengcao/Qwen3-Reranker-0.6B:Q8_0")
        print("   2. 重启Ollama服务: ollama serve")
        print("   3. 重启RAG服务: python main.py")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
