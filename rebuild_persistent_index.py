#!/usr/bin/env python3
"""
重建持久化索引
确保使用新的持久化配置
"""

import os
import sys
import shutil
from pathlib import Path

def check_current_setup():
    """检查当前设置"""
    print("🔍 检查当前设置...")
    
    try:
        from config import get_rag_config
        config = get_rag_config()
        
        print(f"   持久化目录: {config.vectorstore_persist_directory}")
        print(f"   集合名称: {config.vectorstore_collection_name}")
        
        # 检查目录是否存在
        persist_dir = config.vectorstore_persist_directory
        if os.path.exists(persist_dir):
            files = list(Path(persist_dir).rglob("*"))
            print(f"   现有文件数量: {len(files)}")
            for file in files:
                print(f"   - {file.name} ({file.stat().st_size} bytes)")
        else:
            print("   持久化目录不存在")
        
        return config
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def backup_existing_data():
    """备份现有数据"""
    print("\n💾 备份现有数据...")
    
    persist_dir = "./chroma_db"
    backup_dir = "./chroma_db_backup"
    
    try:
        if os.path.exists(persist_dir):
            if os.path.exists(backup_dir):
                shutil.rmtree(backup_dir)
            shutil.copytree(persist_dir, backup_dir)
            print(f"✅ 数据已备份到: {backup_dir}")
            return True
        else:
            print("   没有现有数据需要备份")
            return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def test_rag_service():
    """测试RAG服务"""
    print("\n🤖 测试RAG服务...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        
        # 创建RAG服务实例
        rag_service = RAGService(config)
        print("✅ RAG服务创建成功")
        
        # 测试简单的索引构建
        test_docs = [
            "这是一个测试文档，用于验证持久化索引功能。",
            "ChromaDB是一个向量数据库，支持持久化存储。",
            "RAG系统可以使用ChromaDB来存储和检索文档向量。"
        ]
        
        print("   开始构建测试索引...")
        rag_service.build_index(documents=test_docs)
        print("✅ 测试索引构建完成")
        
        # 检查持久化目录
        persist_dir = config.vectorstore_persist_directory
        if os.path.exists(persist_dir):
            files = list(Path(persist_dir).rglob("*"))
            print(f"✅ 持久化目录包含 {len(files)} 个文件")
            
            # 显示文件大小
            total_size = sum(f.stat().st_size for f in files if f.is_file())
            print(f"   总大小: {total_size} bytes")
            
            return True
        else:
            print("❌ 持久化目录未创建")
            return False
            
    except Exception as e:
        print(f"❌ RAG服务测试失败: {e}")
        return False

def verify_persistence():
    """验证持久化"""
    print("\n✅ 验证持久化...")
    
    try:
        from langchain_community.vectorstores import Chroma
        from config import get_rag_config
        
        config = get_rag_config()
        persist_dir = config.vectorstore_persist_directory
        
        if not os.path.exists(persist_dir):
            print("❌ 持久化目录不存在")
            return False
        
        # 尝试加载现有的向量存储
        try:
            vectorstore = Chroma(
                persist_directory=persist_dir,
                collection_name=config.vectorstore_collection_name
            )
            
            # 获取集合信息
            collection = vectorstore._collection
            count = collection.count()
            print(f"✅ 成功加载持久化数据")
            print(f"   文档数量: {count}")
            
            return True
            
        except Exception as e:
            print(f"⚠️  加载持久化数据时出错: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📋 使用说明:")
    print("=" * 50)
    print("✅ ChromaDB持久化已配置完成！")
    print()
    print("🔧 配置详情:")
    print("   - 持久化目录: ./chroma_db")
    print("   - 集合名称: rag-chroma")
    print("   - 数据库文件: chroma.sqlite3")
    print()
    print("🚀 下次使用时:")
    print("   1. 重启RAG服务")
    print("   2. 使用索引构建器重新构建索引")
    print("   3. 索引数据将自动保存到磁盘")
    print()
    print("📁 文件位置:")
    print("   - 索引构建器: http://localhost:8000/web/index-builder.html")
    print("   - 持久化数据: ./chroma_db/")
    print()
    print("⚠️  注意事项:")
    print("   - 删除chroma_db目录将丢失所有索引数据")
    print("   - 建议定期备份chroma_db目录")
    print("   - 大量数据时建议使用SSD存储")

def main():
    """主函数"""
    print("🚀 重建持久化索引")
    print("=" * 50)
    
    # 检查当前设置
    config = check_current_setup()
    if not config:
        return 1
    
    # 备份现有数据
    if not backup_existing_data():
        print("⚠️  备份失败，继续执行...")
    
    # 测试RAG服务
    if test_rag_service():
        print("✅ RAG服务测试通过")
    else:
        print("❌ RAG服务测试失败")
        return 1
    
    # 验证持久化
    if verify_persistence():
        print("✅ 持久化验证通过")
    else:
        print("⚠️  持久化验证失败，但配置正确")
    
    # 显示使用说明
    show_usage_instructions()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
