# RAG 索引构建器使用指南

## 概述

RAG 索引构建器是一个Web界面工具，用于构建和管理RAG系统的知识库索引。通过这个工具，您可以轻松地添加网页URL和文档内容到您的知识库中。

## 功能特性

### 🌐 URL索引
- 支持批量添加网页URL
- 自动抓取和处理网页内容
- 支持HTTP和HTTPS协议

### 📄 文档索引
- 支持直接输入文档内容
- 支持多文档批量处理
- 灵活的文档分隔方式

### 🚀 后台处理
- 异步索引构建，不阻塞界面
- 实时状态反馈
- 错误处理和提示

## 访问方式

### 直接访问
```
http://localhost:8000/web/index-builder.html
```

### 通过导航访问
1. 访问主页: `http://localhost:8000/`
2. 点击"索引构建器"链接
3. 或从其他页面的导航栏访问

## 使用方法

### 1. URL索引构建

**步骤：**
1. 在"URL索引"区域的文本框中输入网页URL
2. 每行输入一个URL
3. 点击"开始构建索引"按钮

**示例输入：**
```
https://docs.python.org/3/tutorial/
https://fastapi.tiangolo.com/tutorial/
https://langchain.readthedocs.io/en/latest/
```

**支持的URL格式：**
- `https://example.com/page`
- `http://example.com/page`
- 包含路径和参数的完整URL

### 2. 文档内容索引

**步骤：**
1. 在"文档索引"区域的文本框中输入文档内容
2. 使用 `---` 分隔不同的文档
3. 点击"开始构建索引"按钮

**示例输入：**
```
Python是一种高级编程语言，具有简洁的语法和强大的功能。
它广泛应用于Web开发、数据科学、人工智能等领域。

---

FastAPI是一个现代、快速的Web框架，用于构建API。
它基于标准Python类型提示，具有自动API文档生成功能。

---

LangChain是一个用于开发由语言模型驱动的应用程序的框架。
它提供了丰富的组件和工具，简化了AI应用的开发过程。
```

### 3. 混合索引构建

您可以同时提供URL和文档内容：
- 系统会同时处理两种类型的数据
- 构建一个包含所有内容的统一索引

## API接口说明

### 请求格式

**端点：** `POST /index/build`

**请求体：**
```json
{
    "urls": [
        "https://example.com/page1",
        "https://example.com/page2"
    ],
    "documents": [
        "文档内容1",
        "文档内容2"
    ]
}
```

**参数说明：**
- `urls` (可选): 字符串数组，要索引的网页URL列表
- `documents` (可选): 字符串数组，要索引的文档内容列表
- 至少需要提供 `urls` 或 `documents` 中的一个

### 响应格式

**成功响应：**
```json
{
    "message": "索引构建任务已启动",
    "status": "building",
    "urls_count": 2,
    "documents_count": 2
}
```

**错误响应：**
```json
{
    "detail": "错误描述信息"
}
```

## 状态说明

### 构建状态
- **building**: 索引正在后台构建中
- **success**: 索引构建成功完成
- **error**: 索引构建过程中出现错误

### 状态指示器
- 🟢 绿色：操作成功
- 🟡 黄色：处理中
- 🔴 红色：出现错误

## 注意事项

### 输入限制
- URL必须是有效的HTTP/HTTPS地址
- 文档内容不应过长（建议单个文档<10000字符）
- 批量处理时建议控制数量（URL<50个，文档<20个）

### 网络要求
- 需要稳定的网络连接来访问外部URL
- 某些网站可能有访问限制或反爬虫机制

### 性能考虑
- 索引构建是异步进行的，大量数据可能需要较长时间
- 建议分批处理大量数据
- 监控系统资源使用情况

## 故障排除

### 常见问题

**1. 页面无法访问**
- 检查RAG服务是否正常运行
- 确认端口8000未被占用
- 检查防火墙设置

**2. 索引构建失败**
- 检查网络连接
- 验证URL的有效性
- 查看服务器日志获取详细错误信息

**3. 响应缓慢**
- 减少批量处理的数据量
- 检查系统资源使用情况
- 考虑分批次处理

### 调试方法

**1. 检查服务状态**
```bash
curl http://localhost:8000/health
```

**2. 查看日志**
```bash
tail -f logs/rag_system.log
```

**3. 测试API**
```bash
python test_index_builder.py
```

## 最佳实践

### 数据准备
1. **URL选择**：选择内容丰富、结构清晰的页面
2. **文档整理**：确保文档内容完整、格式规范
3. **分类管理**：按主题或类型组织数据

### 索引策略
1. **增量构建**：定期添加新内容而非重建全部索引
2. **质量优先**：优选高质量、权威的数据源
3. **定期维护**：清理过时或无效的索引内容

### 性能优化
1. **批量处理**：合理控制单次处理的数据量
2. **错峰操作**：在系统负载较低时进行大批量索引
3. **监控资源**：关注CPU、内存和存储使用情况

## 相关链接

- [API文档](http://localhost:8000/docs)
- [健康检查](http://localhost:8000/health)
- [标准对话界面](http://localhost:8000/web/index.html)
- [流式对话界面](http://localhost:8000/web/stream.html)
