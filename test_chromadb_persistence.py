#!/usr/bin/env python3
"""
测试ChromaDB持久化功能
"""

import os
import sys
import shutil
from pathlib import Path

def test_config():
    """测试配置"""
    print("🔧 测试配置...")
    
    try:
        from config import get_rag_config
        config = get_rag_config()
        
        print(f"   持久化目录配置: {config.vectorstore_persist_directory}")
        print(f"   集合名称: {config.vectorstore_collection_name}")
        
        if config.vectorstore_persist_directory:
            print("✅ 持久化目录已配置")
            return True, config.vectorstore_persist_directory
        else:
            print("❌ 持久化目录未配置")
            return False, None
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False, None

def test_directory_creation():
    """测试目录创建"""
    print("\n📁 测试目录创建...")
    
    persist_dir = "./chroma_db"
    
    # 如果目录存在，先删除
    if os.path.exists(persist_dir):
        print(f"   删除现有目录: {persist_dir}")
        shutil.rmtree(persist_dir)
    
    try:
        # 创建目录
        os.makedirs(persist_dir, exist_ok=True)
        print(f"✅ 成功创建目录: {persist_dir}")
        
        # 检查目录是否存在
        if os.path.exists(persist_dir) and os.path.isdir(persist_dir):
            print("✅ 目录验证通过")
            return True
        else:
            print("❌ 目录验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 目录创建失败: {e}")
        return False

def test_chromadb_persistence():
    """测试ChromaDB持久化"""
    print("\n🗄️ 测试ChromaDB持久化...")
    
    try:
        from langchain_community.vectorstores import Chroma
        from langchain.schema import Document
        from langchain_openai import OpenAIEmbeddings
        from config import get_rag_config
        
        config = get_rag_config()
        persist_dir = config.vectorstore_persist_directory
        
        # 创建测试文档
        test_docs = [
            Document(page_content="这是一个测试文档，用于验证ChromaDB持久化功能。", metadata={"source": "test1"}),
            Document(page_content="这是第二个测试文档，包含不同的内容。", metadata={"source": "test2"})
        ]
        
        print(f"   使用持久化目录: {persist_dir}")
        print(f"   测试文档数量: {len(test_docs)}")
        
        # 检查是否有OpenAI API密钥
        if not os.getenv("OPENAI_API_KEY"):
            print("⚠️  未设置OPENAI_API_KEY，使用假的嵌入模型")
            # 这里会失败，但我们主要测试持久化目录是否创建
            try:
                vectorstore = Chroma.from_documents(
                    documents=test_docs,
                    embedding=None,  # 这会导致错误
                    persist_directory=persist_dir,
                    collection_name=config.vectorstore_collection_name
                )
            except Exception as e:
                if "embedding" in str(e).lower():
                    print("✅ 预期的嵌入错误（需要OpenAI API密钥）")
                    # 检查目录是否被创建
                    if os.path.exists(persist_dir):
                        print(f"✅ 持久化目录已创建: {persist_dir}")
                        return True
                    else:
                        print(f"❌ 持久化目录未创建: {persist_dir}")
                        return False
                else:
                    raise e
        else:
            print("✅ 检测到OpenAI API密钥")
            try:
                embeddings = OpenAIEmbeddings()
                vectorstore = Chroma.from_documents(
                    documents=test_docs,
                    embedding=embeddings,
                    persist_directory=persist_dir,
                    collection_name=config.vectorstore_collection_name
                )
                print("✅ ChromaDB创建成功")
                
                # 检查目录和文件
                if os.path.exists(persist_dir):
                    files = list(Path(persist_dir).rglob("*"))
                    print(f"✅ 持久化目录已创建，包含 {len(files)} 个文件")
                    return True
                else:
                    print("❌ 持久化目录未创建")
                    return False
                    
            except Exception as e:
                print(f"❌ ChromaDB创建失败: {e}")
                return False
        
    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")
        return False

def test_rag_service_persistence():
    """测试RAG服务持久化"""
    print("\n🤖 测试RAG服务持久化...")
    
    try:
        from rag_service import RAGService
        from config import get_rag_config
        
        config = get_rag_config()
        persist_dir = config.vectorstore_persist_directory
        
        # 清理现有目录
        if os.path.exists(persist_dir):
            shutil.rmtree(persist_dir)
            print(f"   清理现有目录: {persist_dir}")
        
        # 创建RAG服务实例
        rag_service = RAGService(config)
        
        # 测试文档
        test_docs = ["这是一个测试文档，用于验证RAG服务的持久化功能。"]
        
        print("   开始构建索引...")
        try:
            rag_service.build_index(documents=test_docs)
            print("✅ 索引构建完成")
            
            # 检查持久化目录
            if os.path.exists(persist_dir):
                files = list(Path(persist_dir).rglob("*"))
                print(f"✅ 持久化目录已创建: {persist_dir}")
                print(f"   包含文件数量: {len(files)}")
                
                # 列出主要文件
                for file in files[:5]:  # 只显示前5个文件
                    print(f"   - {file.name}")
                if len(files) > 5:
                    print(f"   ... 还有 {len(files) - 5} 个文件")
                
                return True
            else:
                print(f"❌ 持久化目录未创建: {persist_dir}")
                return False
                
        except Exception as e:
            error_msg = str(e)
            if "openai" in error_msg.lower() or "api" in error_msg.lower():
                print("⚠️  需要OpenAI API密钥才能完成测试")
                return False
            else:
                print(f"❌ 索引构建失败: {e}")
                return False
        
    except Exception as e:
        print(f"❌ RAG服务测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    persist_dir = "./chroma_db"
    if os.path.exists(persist_dir):
        try:
            shutil.rmtree(persist_dir)
            print(f"✅ 已清理目录: {persist_dir}")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")

def main():
    """主测试函数"""
    print("🚀 ChromaDB持久化功能测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("配置检查", test_config),
        ("目录创建", test_directory_creation),
        ("ChromaDB持久化", test_chromadb_persistence),
        ("RAG服务持久化", test_rag_service_persistence),
    ]
    
    results = []
    persist_dir = None
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            if test_name == "配置检查":
                result, persist_dir = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed >= 2:  # 至少配置和目录创建通过
        print(f"\n🎉 持久化配置正确！")
        print(f"📁 持久化目录: {persist_dir or './chroma_db'}")
        print("\n💡 建议:")
        print("   1. 重启RAG服务以应用新配置")
        print("   2. 重新构建索引以启用持久化")
        print("   3. 检查chroma_db目录是否创建")
    else:
        print("\n⚠️  持久化配置有问题")
        print("\n🔧 解决方案:")
        print("   1. 检查配置文件中的vectorstore_persist_directory设置")
        print("   2. 确保有写入权限")
        print("   3. 检查OpenAI API密钥设置")
    
    # 询问是否清理
    try:
        response = input("\n是否清理测试文件？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup()
    except KeyboardInterrupt:
        print("\n跳过清理")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
