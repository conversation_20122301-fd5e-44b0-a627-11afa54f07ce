"""
生产环境启动脚本
"""

import os
import sys
import signal
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn
from config import get_server_config, validate_config, print_config
from logger import setup_rag_logging
from monitoring import performance_monitor


def setup_production_environment():
    """设置生产环境"""
    # 验证配置
    try:
        validate_config()
        print("✅ 配置验证通过")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
        sys.exit(1)
    
    # 设置日志
    server_config = get_server_config()
    setup_rag_logging(server_config)
    logger = logging.getLogger(__name__)
    
    # 打印配置信息
    print_config()
    
    # 启动性能监控
    performance_monitor.start_monitoring(interval=60.0)  # 每分钟收集一次系统指标
    logger.info("性能监控已启动")
    
    return logger


def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger(__name__)
    logger.info(f"收到信号 {signum}，正在关闭服务...")
    
    # 停止性能监控
    performance_monitor.stop_monitoring()
    
    # 退出
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 设置生产环境
    logger = setup_production_environment()
    
    # 获取服务器配置
    server_config = get_server_config()
    
    # 生产环境配置
    production_config = {
        "app": "main:app",
        "host": server_config.host,
        "port": server_config.port,
        "workers": int(os.getenv("WORKERS", "4")),  # 工作进程数
        "worker_class": "uvicorn.workers.UvicornWorker",
        "max_requests": int(os.getenv("MAX_REQUESTS", "1000")),  # 每个工作进程处理的最大请求数
        "max_requests_jitter": int(os.getenv("MAX_REQUESTS_JITTER", "100")),
        "timeout_keep_alive": int(os.getenv("TIMEOUT_KEEP_ALIVE", "5")),
        "access_log": True,
        "log_level": server_config.log_level,
        "reload": False,  # 生产环境不启用热重载
    }
    
    logger.info("启动生产环境服务器...")
    logger.info(f"配置: {production_config}")
    
    try:
        # 使用Gunicorn启动（如果可用）
        try:
            import gunicorn.app.wsgiapp as wsgi
            
            # 构建Gunicorn命令行参数
            sys.argv = [
                "gunicorn",
                "--bind", f"{production_config['host']}:{production_config['port']}",
                "--workers", str(production_config['workers']),
                "--worker-class", production_config['worker_class'],
                "--max-requests", str(production_config['max_requests']),
                "--max-requests-jitter", str(production_config['max_requests_jitter']),
                "--timeout", str(production_config['timeout_keep_alive']),
                "--access-logfile", "-",
                "--log-level", production_config['log_level'],
                production_config['app']
            ]
            
            logger.info("使用Gunicorn启动服务器")
            wsgi.run()
            
        except ImportError:
            # 如果没有Gunicorn，使用Uvicorn
            logger.warning("Gunicorn不可用，使用Uvicorn启动")
            
            uvicorn.run(
                production_config['app'],
                host=production_config['host'],
                port=production_config['port'],
                log_level=production_config['log_level'],
                access_log=production_config['access_log'],
                reload=production_config['reload']
            )
            
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
